{"https://developer.android.com/google/play/billing/integrate": {"hash": "b96e8d491eb279f1a14a93da169bac83", "last_checked": "2025-07-18T17:46:50.196886", "content": "Integrate the Google Play Billing Library into your app | Google Play's billing system | Android Developers Skip to main content Essentials Build AI experiences Build AI-powered Android apps with Gemini APIs and more. Get started Get started Start by creating your first app. Go deeper with our training courses or explore app development on your own. Hello world Training courses Tutorials Compose for teams Kotlin for Android Monetization with Play ↗️ Extend by device Build apps that give your users seamless experiences from phones to tablets, watches, headsets, and more. Adaptive apps Android XR Wear OS Android for Cars Android TV ChromeOS Build by category Learn to build for your use case by following Google's prescriptive and opinionated guidance. Games Camera & media Social & messaging Health & fitness Productivity Enterprise apps Get the latest Stay in touch with the latest releases throughout the year, join our preview programs, and give us your feedback. Latest updates Experimental updates Android Studio preview Jetpack & Compose libraries Wear OS releases Privacy Sandbox ↗️ Design & Plan Excellent Experiences Build the best experiences for your best users. Learn more UI Design Design a beautiful user interface using Android best practices. Design for Android Mobile Adaptive UI Android XR Widgets Wear OS Android TV Architecture Design robust, testable, and maintainable app logic and services. Introduction Libraries Navigation Modularization Testing Quality Plan for app quality and align with Play store guidelines. Overview Core value User experience Accessibility Technical quality Excellent Experiences Security Safeguard users against threats and ensure a secure Android experience. Overview Privacy Permissions Identity Fraud prevention Develop Gemini in Android Studio Your AI development companion for Android development. Learn more Get Android Studio Core areas Get the samples and docs for the features you need. Samples User interfaces Background work Data and files Connectivity All core areas ⤵️ Tools and workflow Use the IDE to write and build your app, or create your own pipeline. Write and debug code Build projects Test your app Performance Command-line tools Gradle plugin API Device tech Write code for form factors. Connect devices and share data. Adaptive UI Wear OS Android XR Android Health Android for Cars Android TV All devices ⤵️ Libraries Browse API reference documentation with all the details. Android platform Jetpack libraries Compose libraries Google Play services ↗️ Google Play SDK index ↗️ Google Play Community / English Deutsch Español – América Latina Français Indonesia Italiano Polski Português – Brasil Tiếng Việt Türkçe Русский עברית العربيّة فارسی हिंदी বাংলা ภาษาไทย 中文 – 简体 中文 – 繁體 日本語 한국어 Android Studio Sign in Google Play Overview Play Console Play Billing Play Integrity Play Policies Play Services Brand & marketing Other Play guides Essentials More Design & Plan More Develop More Google Play Overview Play Console Play Billing Play Integrity Play Policies Play Services Brand & marketing Other Play guides Community Android Studio Sell digital content in apps Google Play's billing system About Google Play's billing system Setup Additional resources Play Billing Library Migrate to Billing Library 8 Play Billing Library reference Play Billing Library release notes Library version deprecation FAQ App integration Integrate the library Handle BillingResult response codes Server backend integration About server backend integration Purchase lifecycle and RTDNs One-time products About one-time products One-time product purchase lifecycle Multiple purchase options and offers Subscriptions About subscriptions Subscription with add-ons Subscription lifecycle Change subscription prices Manage subscriptions Google Play Developer API API reference Real-time developer notifications reference Play Developer API release notes API deprecations Best practices Manage your product catalog Run offers and promotions Fight fraud and abuse Developer Payload QueryPurchaseHistory Testing and troubleshooting Test your integration Test BillingResult response codes Monetizing outside of Google Play Billing Alternative Billing About alternative billing In-app integration with User Choice In-app integration without User Choice Interim UX guidelines User choice billing pilot Alternative in-app billing systems Alternative billing without user choice (EEA program) External offers About the program In-app integration Backend Integration Build AI experiences Get started Get started Hello world Training courses Tutorials Compose for teams Kotlin for Android Monetization with Play ↗️ Extend by device Adaptive apps Android XR Wear OS Android for Cars Android TV ChromeOS Build by category Games Camera & media Social & messaging Health & fitness Productivity Enterprise apps Get the latest Latest updates Experimental updates Android Studio preview Jetpack & Compose libraries Wear OS releases Privacy Sandbox ↗️ Excellent Experiences Learn more UI Design Design for Android Mobile Adaptive UI Android XR Widgets Wear OS Android TV Architecture Introduction Libraries Navigation Modularization Testing Quality Overview Core value User experience Accessibility Technical quality Excellent Experiences Security Overview Privacy Permissions Identity Fraud prevention Gemini in Android Studio Learn more Get Android Studio Core areas Samples User interfaces Background work Data and files Connectivity All core areas ⤵️ Tools and workflow Write and debug code Build projects Test your app Performance Command-line tools Gradle plugin API Device tech Adaptive UI Wear OS Android XR Android Health Android for Cars Android TV All devices ⤵️ Libraries Android platform Jetpack libraries Compose libraries Google Play services ↗️ Google Play SDK index ↗️ Reminder: By Aug 31, 2025, all new apps and updates to existing apps must use Billing Library version 7 or newer. If you need more time to update your app, you can request an extension until Nov 1, 2025. Learn about Play Billing Library version deprecation . Google Play's billing system Google Play Play Billing Integrate the Google Play Billing Library into your app Stay organized with collections Save and categorize content based on your preferences. Note: Before reading this document, make sure you've read through the Play Console Help Center documentation , which describes critical purchase-related concepts, as well as how to create and configure your products for sale. In addition, be sure you've set up your Google Play configuration beforehand by following the steps in Getting ready . This document describes how to integrate the Google Play Billing Library into your app to start selling products. Life of a purchase Here's a typical purchase flow for a one-time purchase or a subscription. Show the user what they can buy. Launch the purchase flow for the user to accept the purchase. Verify the purchase on your server. Give content to the user. Acknowledge delivery of the content. For consumable products, consume the purchase so that the user can buy the item again. Subscriptions automatically renew until they are canceled. A subscription can go through the following states: Active : User is in good standing and has access to the subscription. Cancelled : User has cancelled but still has access until expiration. In grace period : User experienced a payment issue but still has access while Google is retrying the payment method. On hold : User experienced a payment issue and no longer has access while Google is retrying the payment method. Paused : User paused their access and does not have access until they resume. Expired : User has cancelled and lost access to the subscription. The user is considered churned at expiration. Initialize a connection to Google Play The first step to integrate with Google Play's billing system is to add the Google Play Billing Library to your app and initialize a connection. Add the Google Play Billing Library dependency Note: If you've followed the Getting ready guide, then you've already added the necessary dependencies and can move on to the next section. Add the Google Play Billing Library dependency to your app's build.gradle file as shown: Groovy dependencies { def billing_version = \"8.0.0\" implementation \"com.android.billingclient:billing:$billing_version\" } Kotlin dependencies { val billing_version = \"8.0.0\" implementation ( \"com.android.billingclient:billing: $ billing_version \" ) } If you're using Kotlin, the Google Play Billing Library KTX module contains Kotlin extensions and coroutines support that enable you to write idiomatic Kotlin when using the Google Play Billing Library. To include these extensions in your project, add the following dependency to your app's build.gradle file as shown: Groovy dependencies { def billing_version = \"8.0.0\" implementation \"com.android.billingclient:billing-ktx:$billing_version\" } Kotlin dependencies { val billing_version = \"8.0.0\" implementation ( \"com.android.billingclient:billing-ktx: $ billing_version \" ) } Initialize a BillingClient Once you've added a dependency on the Google Play Billing Library, you need to initialize a BillingClient instance. BillingClient is the main interface for communication between the Google Play Billing Library and the rest of your app. BillingClient provides convenience methods, both synchronous and asynchronous, for many common billing operations. Make note of the following: It's recommended that you have one active BillingClient connection open at one time to avoid multiple PurchasesUpdatedListener callbacks for a single event. It's recommended to initiate a connection for the BillingClient when your app is launched or comes to the foreground to ensure your app processes purchases in a timely manner. This can be accomplished by using ActivityLifecycleCallbacks registered by registerActivityLifecycleCallbacks and listening for onActivityResumed to initialize a connection when you first detect an activity being resumed. Refer to the section on processing purchases for more details on why this best practice should be followed. Also remember to end the connection when your app is closed. To create a BillingClient , use newBuilder . You can pass any context to newBuilder() , and BillingClient uses it to get an application context. That means you don't need to worry about memory leaks. To receive updates on purchases, you must also call setListener , passing a reference to a PurchasesUpdatedListener . This listener receives updates for all purchases in your app. Kotlin private val purchasesUpdatedListener = PurchasesUpdatedListener { billingResult , purchases - > // To be implemented in a later section. } private var billingClient = BillingClient . newBuilder ( context ) . setListener ( purchasesUpdatedListener ) // Configure other settings. . build () Java private PurchasesUpdatedListener purchasesUpdatedListener = new PurchasesUpdatedListener () { @Override public void onPurchasesUpdated ( BillingResult billingResult , List<Purchase> purchases ) { // To be implemented in a later section. } }; private BillingClient billingClient = BillingClient . newBuilder ( context ) . setListener ( purchasesUpdatedListener ) // Configure other settings. . build (); Note: The Google Play Billing Library returns errors in the form of BillingResult . A BillingResult contains a BillingResponseCode , which categorizes possible billing-related errors that your app can encounter. For example, if you receive a SERVICE_DISCONNECTED error code, your app should reinitialize the connection with Google Play. Additionally, a BillingResult contains a debug message , which is useful during development to diagnose errors. Connect to Google Play After you have created a BillingClient , you need to establish a connection to Google Play. To connect to Google Play, call startConnection . The connection process is asynchronous, and you must implement a BillingClientStateListener to receive a callback once the setup of the client is complete and it's ready to make further requests. You must also implement retry logic to handle lost connections to Google Play. To implement retry logic, override the onBillingServiceDisconnected() callback method, and make sure that the BillingClient calls the startConnection() method to reconnect to Google Play before making further requests. The following example demonstrates how to start a connection and test that it's ready to use: Kotlin billingClient . startConnection ( object : BillingClientStateListener { override fun onBillingSetupFinished ( billingResult : BillingResult ) { if ( billingResult . responseCode == BillingResponseCode . OK ) { // The BillingClient is ready. You can query purchases here. } } override fun onBillingServiceDisconnected () { // Try to restart the connection on the next request to // Google Play by calling the startConnection() method. } }) Java billingClient . startConnection ( new BillingClientStateListener () { @Override public void onBillingSetupFinished ( BillingResult billingResult ) { if ( billingResult . getResponseCode () == BillingResponseCode . OK ) { // The BillingClient is ready. You can query purchases here. } } @Override public void onBillingServiceDisconnected () { // Try to restart the connection on the next request to // Google Play by calling the startConnection() method. } }); Note: It's strongly recommended that you use the automatic service reconnection feature, as described in the following section. If you choose not to use this feature, it's strongly recommended that you implement your own connection retry logic and override the onBillingServiceDisconnected() method. In either case, make sure you maintain the BillingClient connection when executing any methods. Automatically Re-establish a Connection With the introduction of the enableAutoServiceReconnection() method in BillingClient.Builder in version 8.0.0, the Play Billing Library can now automatically re-establish the service connection if an API call is made while the service is disconnected. This can lead to a reduction in SERVICE_DISCONNECTED responses since the reconnection is handled internally before the API call is made. How to enable automatic reconnection When building a BillingClient instance, use the enableAutoServiceReconnection() method in the BillingClient.Builder to enable automatic reconnection. Kotlin val billingClient = BillingClient . newBuilder ( context ) . setListener ( listener ) . enablePendingPurchases () . enableAutoServiceReconnection () // Add this line to enable reconnection . build () Java BillingClient billingClient = BillingClient . newBuilder ( context ) . setListener ( listener ) . enablePendingPurchases () . enableAutoServiceReconnection () // Add this line to enable reconnection . build (); Show products available to buy After you have established a connection to Google Play, you are ready to query for your available products and display them to your users. Querying for product details is an important step before displaying your products to your users, as it returns localized product information. For subscriptions, verify your product display follows all Play policies . To query for one-time product details, call the queryProductDetailsAsync method. This method can return multiple offers based on your one-time product configuration. For more information, see Multiple purchase options and offers for one-time products . To handle the result of the asynchronous operation, you must also specify a listener which implements the ProductDetailsResponseListener interface. You can then override onProductDetailsResponse , which notifies the listener when the query finishes, as shown in the following example: Kotlin val queryProductDetailsParams = QueryProductDetailsParams . newBuilder () . setProductList ( ImmutableList . of ( Product . newBuilder () . setProductId ( \"product_id_example\" ) . setProductType ( ProductType . SUBS ) . build ())) . build () billingClient . queryProductDetailsAsync ( queryProductDetailsParams ) { billingResult , queryProductDetailsResult - > if ( billingResult . getResponseCode () == BillingResponseCode . OK ) { for ( ProductDetails productDetails : queryProductDetailsResult . getProductDetailsList ()) { // Process successfully retrieved product details here. } for ( UnfetchedProduct unfetchedProduct : queryproductDetailsResult . getUnfetchedProductList ()) { // Handle any unfetched products as appropriate. } } } Java QueryProductDetailsParams queryProductDetailsParams = QueryProductDetailsParams . newBuilder () . setProductList ( ImmutableList . of ( Product . newBuilder () . setProductId ( \"product_id_example\" ) . setProductType ( ProductType . SUBS ) . build ())) . build (); billingClient . queryProductDetailsAsync ( queryProductDetailsParams , new ProductDetailsResponseListener () { public void onProductDetailsResponse ( BillingResult billingResult , QueryProductDetailsResult queryProductDetailsResult ) { if ( billingResult . getResponseCode () == BillingResponseCode . OK ) { for ( ProductDetails productDetails : queryProductDetailsResult (). getProductDetailsList ()) { // Process success retrieved product details here. } for ( UnfetchedProduct unfetchedProduct : queryproductDetailsResult . getUnfetchedProductList ()) { // Handle any unfetched products as appropriate. } } } } ) When querying for product details, pass an instance of QueryProductDetailsParams that specifies a list of product ID strings created in Google Play Console along with a ProductType . The ProductType can be either ProductType.INAPP for one-time products or ProductType.SUBS for subscriptions. Query with Kotlin extensions If you're using Kotlin extensions , you can query for one-time product details by calling the queryProductDetails() extension function. queryProductDetails() leverages Kotlin coroutines so that you don't need to define a separate listener. Instead, the function suspends until the querying completes, after which you can process the result: suspend fun processPurchases () { val productList = listOf ( QueryProductDetailsParams . Product . newBuilder () . setProductId ( \"product_id_example\" ) . setProductType ( BillingClient . ProductType . SUBS ) . build () ) val params = QueryProductDetailsParams . newBuilder () params . setProductList ( productList ) // leverage queryProductDetails Kotlin extension function val productDetailsResult = withContext ( Dispatchers . IO ) { billingClient . queryProductDetails ( params . build ()) } // Process the result. } Rarely, some devices are unable to support ProductDetails and queryProductDetailsAsync() , usually due to outdated versions of Google Play Services . To provide proper support for this scenario, learn how to use backwards compatibility features in the Play Billing Library 7 migration guide . Process the result The Google Play Billing Library stores the query results in a QueryProductDetailsResult object. QueryProductDetailsResult contains a List of ProductDetails objects. You can then call a variety of methods on each ProductDetails object in the list to view relevant information about a successfully fetched one-time product, such as its price or description. To view the available product detail information, see the list of methods in the ProductDetails class. QueryProductDetailsResult also contains a List of UnfetchedProduct objects. You can then query each UnfetchedProduct to get a status code corresponding to the fetch failure reason. To view the available unfetched product information, see the list of methods in the UnfetchedProduct class. Before offering an item for sale, check that the user does not already own the item. If the user has a consumable that is still in their item library, they must consume the item before they can buy it again. Before offering a subscription, verify that the user is not already subscribed. Also note the following: For subscriptions, the queryProductDetailsAsync() method returns subscription product details and a maximum of 50 user eligible offers per subscription. If the user attempts to purchase an ineligible offer (for example, if the app is displaying an outdated list of eligible offers), Play informs the user that they are ineligible, and the user can choose to purchase the base plan instead. For one-time products, the queryProductDetailsAsync() method returns only the user eligible offers. If the user attempts to purchase an offer for which they're ineligible (for example, if the user has reached the purchase quantity limit), Play informs the user that they are ineligible, and the user can choose to purchase its purchase option offer instead. Note: Caching ProductDetails objects is not recommended, as stale objects can cause launchBillingFlow() failures. Note: Some Android devices might have an older version of the Google Play Store app that doesn't support certain products types, such as subscriptions. Before your app enters the billing flow, you can call isFeatureSupported() to determine whether the device supports the products you want to sell. For a list of product types that can be supported, see BillingClient.FeatureType . Launch the purchase flow To start a purchase request from your app, call the launchBillingFlow() method from your app's main thread. This method takes a reference to a BillingFlowParams object that contains the relevant ProductDetails object obtained from calling queryProductDetailsAsync . To create a BillingFlowParams object, use the BillingFlowParams.Builder class. Kotlin // An activity reference from which the billing flow will be launched. val activity : Activity = ...; val productDetailsParamsList = listOf ( BillingFlowParams . ProductDetailsParams . newBuilder () // retrieve a value for \"productDetails\" by calling queryProductDetailsAsync() . setProductDetails ( productDetails ) // Get the offer token: // a. For one-time products, call ProductDetails.getOneTimePurchaseOfferDetailsList() // for a list of offers that are available to the user. // b. For subscriptions, call ProductDetails.subscriptionOfferDetails() // for a list of offers that are available to the user. . setOfferToken ( selectedOfferToken ) . build () ) val billingFlowParams = BillingFlowParams . newBuilder () . setProductDetailsParamsList ( productDetailsParamsList ) . build () // Launch the billing flow val billingResult = billingClient . launchBillingFlow ( activity , billingFlowParams ) Java // An activity reference from which the billing flow will be launched. Activity activity = ...; ImmutableList<ProductDetailsParams> productDetailsParamsList = ImmutableList . of ( ProductDetailsParams . newBuilder () // retrieve a value for \"productDetails\" by calling queryProductDetailsAsync() . setProductDetails ( productDetails ) // Get the offer token: // a. For one-time products, call ProductDetails.getOneTimePurchaseOfferDetailsList() // for a list of offers that are available to the user. // b. For subscriptions, call ProductDetails.subscriptionOfferDetails() // for a list of offers that are available to the user. . setOfferToken ( selectedOfferToken ) . build () ); BillingFlowParams billingFlowParams = BillingFlowParams . newBuilder () . setProductDetailsParamsList ( productDetailsParamsList ) . build (); // Launch the billing flow BillingResult billingResult = billingClient . launchBillingFlow ( activity , billingFlowParams ); The launchBillingFlow() method returns one of several response codes listed in BillingClient.BillingResponseCode . Be sure to check this result to verify there were no errors launching the purchase flow. A BillingResponseCode of OK indicates a successful launch. On a successful call to launchBillingFlow() , the system displays the Google Play purchase screen. Figure 1 shows a purchase screen for a subscription: Figure 1. The Google Play purchase screen shows a subscription that is available for purchase. Google Play calls onPurchasesUpdated() to deliver the result of the purchase operation to a listener that implements the PurchasesUpdatedListener interface. The listener is specified using the setListener() method when you initialized your client . You must implement onPurchasesUpdated() to handle possible response codes. The following example shows how to override onPurchasesUpdated() : Kotlin override fun onPurchasesUpdated ( billingResult : BillingResult , purchases : List<Purchase>?) { if ( billingResult . responseCode == BillingResponseCode . OK && purchases != null ) { for ( purchase in purchases ) { // Process the purchase as described in the next section. } } else if ( billingResult . responseCode == BillingResponseCode . USER_CANCELED ) { // Handle an error caused by a user canceling the purchase flow. } else { // Handle any other error codes. } } Java @Override void onPurchasesUpdated ( BillingResult billingResult , List<Purchase> purchases ) { if ( billingResult . getResponseCode () == BillingResponseCode . OK && purchases != null ) { for ( Purchase purchase : purchases ) { // Process the purchase as described in the next section. } } else if ( billingResult . getResponseCode () == BillingResponseCode . USER_CANCELED ) { // Handle an error caused by a user canceling the purchase flow. } else { // Handle any other error codes. } } A successful purchase generates a Google Play purchase success screen similar to figure 2. Figure 2. Google Play's purchase success screen. A successful purchase also generates a purchase token, which is a unique identifier that represents the user and the product ID for the one-time product they purchased. Your apps can store the purchase token locally, though we strongly recommend passing the token to your secure backend server where you can then verify the purchase and protect against fraud. This process is further described in Detecting and Processing Purchases . The user is also emailed a receipt of the transaction containing an Order ID or a unique ID of the transaction. Users receive an email with a unique Order ID for each one-time product purchase, and also for the initial subscription purchase and subsequent recurring automatic renewals. You can use the Order ID to manage refunds in the Google Play Console. Indicate a personalized price If your app can be distributed to users in the European Union, use the setIsOfferPersonalized() method when calling launchBillingFlow to disclose to users that an item's price was personalized using automated decision-making. Figure 3. The Google Play purchase screen indicating that the price was customized for the user. You must consult Art. 6 (1) (ea) CRD of the Consumer Rights Directive 2011/83/EU to determine if the price you are offering to users is personalized. setIsOfferPersonalized() takes a boolean input. When true , the Play UI includes the disclosure. When false , the UI omits the disclosure. The default value is false . See the Consumer Help Center for more information. Attach user identifiers When you launch the purchase flow your app can attach any user identifiers you have for the user making the purchase using obfuscatedAccountId or obfuscatedProfileId . An example identifier could be an obfuscated version of the user's login in your system. Setting these parameters can help Google detect fraud . Additionally, it can help you ensure that purchases are attributed to the right user as discussed in granting entitlements to users . Detect and process purchases The detection and processing of a purchase described in this section is applicable to all types of purchases including out of app purchases like promotion redemptions. Your app detect new purchases and completed pending purchases in one of the following ways: When onPurchasesUpdated is called as a result of your app calling launchBillingFlow (as discussed in the previous section) or if your app is running with an active Billing Library connection when there is a purchase made outside your app or a pending purchase is completed. For example, a family member approves a pending purchase on another device. When your app calls queryPurchasesAsync to query the user's purchases. For #1 onPurchasesUpdated will automatically be called for new or completed purchases as long as your app is running and has an active Google Play Billing Library connection. If your application is not running or your app doesn't have an active Google Play Billing library connection, onPurchasesUpdated won't be invoked. Remember, it is recommended for your app to try to keep an active connection as long as your app is in the foreground so that your app gets timely purchase updates. For #2 you must call BillingClient.queryPurchasesAsync() to ensure your app processes all purchases. It is recommended that you do this when your app successfully establishes a connection with the Google Play Billing Library (which is recommended when your app is launched or comes to the foreground as discussed in initialize a BillingClient . This can be accomplished by calling queryPurchasesAsync when receiving a successful result to onServiceConnected . Following this recommendation is critical to handle events and situations such as: Network Issues during the purchase : A user can make a successful purchase and receive confirmation from Google, but their device loses network connectivity before their device and your app receives notification of the purchase through the PurchasesUpdatedListener . Multiple devices : A user may buy an item on one device and then expect to see the item when they switch devices. Handling purchases made outside your app : Some purchases, such as promotion redemptions, can be made outside your app. Handling purchase state transitions : A user may complete payment for a PENDING purchase while your application is not running and expect to receive confirmation that they completed the purchase when they open your app. Note: You can also choose to listen to Real-time Developer Notifications to learn about new purchases and completed pending purchases in real-time, even if the user is not using your app when the event occurs. This helps you keep your backend state in sync at all times. This is described in more detail in the backend integration section for one-time purchases and subscriptions . Once your app detects a new or completed purchase then your app should: Verify the purchase. Grant content to the user for completed purchases. Notify the user. Notify Google that your app processed completed purchases. These steps are discussed in detail in the following sections followed by a section to recap all the steps. Verify the purchase Your app should always verify the legitimacy of purchases before granting benefits to a user. This can be done by following the guidelines described in Verify purchases before granting entitlements . Only after verifying the purchase should your app continue to process the purchase and grant entitlements to the user, which is discussed in the next section. Grant entitlement to the user Once your app has verified a purchase it can continue to grant the entitlement to the user and notify the user. Before granting entitlement, verify that your app is checking that the purchase state is PURCHASED . If the purchase is in PENDING state, your app should notify the user that they still need to complete actions to complete the purchase before entitlement is granted. Only grant entitlement when the purchase transitions from PENDING to SUCCESS. Additional information can be found in Handling pending transactions . If you have attached user identifiers to the purchase as discussed in attaching user identifiers you can retrieve and use them to attribute to the correct user in your system. This technique is useful when reconciling purchases where your app may have lost context about which user a purchase is for. Note, purchases made outside your app won't have these identifiers set. In this case your app can either grant the entitlement to the logged in user, or prompt the user to select a preferred account. For pre-orders , the purchase is in PENDING state before the release time is reached. The preorder purchase will complete at the release time and change the state to PURCHASED without additional actions. Notify the User After granting entitlement to the user, your app should show a notification to acknowledge the successful purchase. Because of the notification, the user is not confused as to whether the purchase completed successfully, which could result in the user stopping using your app, contacting user support, or complaining about it on social media. Be aware that your app may detect purchase updates at any time during your application lifecycle. For example, a parent approves a pending purchase on another device, in which case your app may want to delay notifying the user to an appropriate time. Some examples where a delay would be appropriate are: During the action part of a game or cutscenes, showing a message may distract the user. In this case, you must notify the user after the action part is over. During the initial tutorial and user setup parts of the game. For example, a user may have made a purchase outside your app before installing it. We recommend you notify new users of the reward immediately after they open the game or during initial user setup. If your app requires the user to create an account or logging in before granting entitlement to the user it is recommended to communicate to your user which steps to complete to claim their purchase. This is critical since purchases are refunded after 3 days if your app has not processed the purchase. When notifying the user about a purchase, Google Play recommends the following mechanisms: Show an in-app dialog. Deliver the message to an in-app message box, and clearly stating that there is a new message in the in-app message box. Use an OS notification message. The notification should notify the user about the benefit they received. For example, \"You purchased 100 Gold Coins!\". Additionally, if the purchase was a result of a benefit of a program such as Play Pass your app communicates this to the user. For example \"Items received! You just got 100 Gems with Play Pass. Continue.\". Each program may have guidance on the recommended text to display to users to communicate benefits. Notify Google the purchase was processed After your app has granted entitlement to the user and notified them about the successful transaction, your app needs to notify Google that the purchase was successfully processed. This is done by acknowledging the purchase and must be done within three days so that the purchase isn't automatically refunded and entitlement revoked . The process for acknowledging different types of purchases is described in the following sections. Consumable products For consumables, if your app has a secure backend, we recommend that you use Purchases.products:consume to reliably consume purchases. Make sure the purchase wasn't already consumed by checking the consumptionState from the result of calling Purchases.products:get . If your app is client-only without a backend, use consumeAsync() from the Google Play Billing Library. Both methods fulfill the acknowledgement requirement and indicate that your app has granted entitlement to the user. These methods also enable your app to make the one-time product corresponding to the input purchase token available for repurchase. With consumeAsync() you must also pass an object that implements the ConsumeResponseListener interface. This object handles the result of the consumption operation. You can override the onConsumeResponse() method, which the Google Play Billing Library calls when the operation is complete. The following example illustrates consuming a product with the Google Play Billing Library using the associated purchase token: Kotlin val consumeParams = ConsumeParams . newBuilder () . setPurchaseToken ( purchase . getPurchaseToken ()) . build () val consumeResult = withContext ( Dispatchers . IO ) { client . consumePurchase ( consumeParams ) } Java ConsumeParams consumeParams = ConsumeParams . newBuilder () . setPurchaseToken ( purchase . getPurchaseToken ()) . build (); ConsumeResponseListener listener = new ConsumeResponseListener () { @Override public void onConsumeResponse ( BillingResult billingResult , String purchaseToken ) { if ( billingResult . getResponseCode () == BillingResponseCode . OK ) { // Handle the success of the consume operation. } } }; billingClient . consumeAsync ( consumeParams , listener ); Note: Purchases made using Play Points are auto-acknowledged, and you don't need to acknowledge such purchases using the acknowledge flow. As consumption requests can occasionally fail, you must check your secure backend server to verify that each purchase token hasn't been used so your app doesn't grant entitlement multiple times for the same purchase. Alternatively, your app can wait until you receive a successful consumption response from Google Play before granting entitlement. If you choose to withhold purchases from the user until Google Play sends a successful consumption response, you must be very careful not to lose track of the purchases for which you have sent a consumption request. Non-consumable products To acknowledge non-consumable purchases, if your app has a secure backend, we recommend using Purchases.products:acknowledge to reliably acknowledge purchases. Make sure the purchase hasn't been previously acknowledged by checking the acknowledgementState from the result of calling Purchases.products:get . If your app is client-only, use BillingClient.acknowledgePurchase() from the Google Play Billing Library in your app. Before acknowledging a purchase, your app should check whether it was already acknowledged by using the isAcknowledged() method in the Google Play Billing Library. The following example shows how to acknowledge a purchase using the Google Play Billing Library: Kotlin val client : BillingClient = ... val acknowledgePurchaseResponseListener : AcknowledgePurchaseResponseListener = ... val acknowledgePurchaseParams = AcknowledgePurchaseParams . newBuilder () . setPurchaseToken ( purchase . purchaseToken ) val ackPurchaseResult = withContext ( Dispatchers . IO ) { client . acknowledgePurchase ( acknowledgePurchaseParams . build ()) } Java BillingClient client = ... AcknowledgePurchaseResponseListener acknowledgePurchaseResponseListener = ... AcknowledgePurchaseParams acknowledgePurchaseParams = AcknowledgePurchaseParams . newBuilder () . setPurchaseToken ( purchase . getPurchaseToken ()) . build (); client . acknowledgePurchase ( acknowledgePurchaseParams , acknowledgePurchaseResponseListener ); Subscriptions Subscriptions are handled similarly to non-consumables. If possible, use Purchases.subscriptions.acknowledge from the Google Play Developer API to reliably acknowledge the purchase from your secure backend. Verify that the purchase hasn't been previously acknowledged by checking the acknowledgementState in the purchase resource from Purchases.subscriptions:get . Otherwise, you can acknowledge a subscription using BillingClient.acknowledgePurchase() from the Google Play Billing Library after checking isAcknowledged() . All initial subscription purchases need to be acknowledged. Subscription renewals don't need to be acknowledged. For more information about when subscriptions need to be acknowledged, see the Sell subscriptions topic. Recap The following code snippet shows a recap of these steps. Kotlin fun handlePurchase ( Purchase purchase ) { // Purchase retrieved from BillingClient#queryPurchasesAsync or your // onPurchasesUpdated. Purchase purchase = ...; // Step 1: Send the purchase to your secure backend to verify the purchase // following // https://developer.android.com/google/play/billing/security#verify . // Step 2: Update your entitlement storage with the purchase. If purchase is // in PENDING state then ensure the entitlement is marked as pending and the // user does not receive benefits yet. It is recommended that this step is // done on your secure backend and can combine in the API call to your // backend in step 1. // Step 3: Notify the user using appropriate messaging (delaying // notification if needed as discussed above). // Step 4: Notify Google the purchase was processed using the steps // discussed in the processing purchases section. } Java void handlePurchase ( Purchase purchase ) { // Purchase retrieved from BillingClient#queryPurchasesAsync or your // onPurchasesUpdated. Purchase purchase = ...; // Step 1: Send the purchase to your secure backend to verify the purchase // following // https://developer.android.com/google/play/billing/security#verify // Step 2: Update your entitlement storage with the purchase. If purchase is // in PENDING state then ensure the entitlement is marked as pending and the // user does not receive benefits yet. It is recommended that this step is // done on your secure backend and can combine in the API call to your // backend in step 1. // Step 3: Notify the user using appropriate messaging (delaying // notification if needed as discussed above). // Step 4: Notify Google the purchase was processed using the steps // discussed in the processing purchases section. } To verify your app has correctly implemented these steps you can follow the testing guide . Handle pending transactions Google Play supports pending transactions , or transactions that require one or more additional steps between when a user initiates a purchase and when the payment method for the purchase is processed. Your app shouldn't grant entitlement to these types of purchases until Google notifies you that the user's payment method was successfully charged. For example, a user can initiate a transaction by choosing a physical store where they'll pay later with cash. The user receives a code through both notification and email. When the user arrives at the physical store, they can redeem the code with the cashier and pay with cash. Google then notifies both you and the user that payment has been received. Your app can then grant entitlement to the user. Call enablePendingPurchases() as part of initializing the BillingClient to enable pending transactions for your app. Your app must enable and support pending transactions for one-time products. Before adding support, be sure you understand the purchase lifecycle for pending transactions. Note: For apps using Google Play Billing Library version 7.0 and higher, you can enable pending transactions for subscription prepaid plan purchases. See Handle subscription pending transactions for how to support in your app. When your app receives a new purchase, either through your PurchasesUpdatedListener or as a result of calling queryPurchasesAsync , use the getPurchaseState() method to determine whether the purchase state is PURCHASED or PENDING . You should grant entitlement only when the state is PURCHASED . If your app is running and you have an active Play Billing Library connection when the user completes the purchase, your PurchasesUpdatedListener is called again, and the PurchaseState is now PURCHASED . At this point, your app can process the purchase using the standard method for Detecting and Processing Purchases . Your app should also call queryPurchasesAsync() in your app's onResume() method to handle purchases that have transitioned to the PURCHASED state while your app was not running. Note: You should acknowledge a purchase only when the state is PURCHASED , i.e. Don't acknowledge it while a purchase is in PENDING state. The three day acknowledgement window begins only when the purchase state transitions from 'PENDING' to 'PURCHASED'. When the purchase transitions from PENDING to PURCHASED , your real_time_developer_notifications client receives a ONE_TIME_PRODUCT_PURCHASED or SUBSCRIPTION_PURCHASED notification. If the purchase is cancelled, you will receive a ONE_TIME_PRODUCT_CANCELED or SUBSCRIPTION_PENDING_PURCHASE_CANCELED notification. This can happen if your customer does not complete payment in the required timeframe. Note that you can always use the Google Play Developer API to check the current state of a purchase. Note: Pending transactions can be tested with application licensing. In addition to two test credit cards, license testers have access to two test instruments for delayed forms of payment where the payment automatically completes or cancels after a couple of minutes. You can find detailed steps on how to test this scenario at Test pending transactions . Handle multi-quantity purchases Supported in versions 4.0 and higher of the Google Play Billing Library, Google Play allows customers to purchase more than one of the same one-time product in one transaction by specifying a quantity from the purchase cart. Your app is expected to handle multi-quantity purchases and grant entitlement based on the specified purchase quantity. Note: Multi-quantity is meant for consumable one-time products, products that can be purchased, consumed, and purchased again. Don't enable this feature for products that are not meant to be purchased repeatedly. And in the case of offers, multi-quantity purchases are only supported for purchase options (buy or rent) and not offers . To honor multi-quantity purchases, your app's provisioning logic needs to check for an item quantity. You can access a quantity field from one of the following APIs: getQuantity() from the Google Play Billing Library. Purchases.products.quantity from the Google Play Developer API After you've added logic to handle multi-quantity purchases, you then need to enable the multi-quantity feature for the corresponding product on the one-time product management page in the Google Play Developer Console. Note: Be sure your app honors multi-quantity purchases before enabling the feature in the console. You might need to force an update to a version of your app that provides support before you can enable the feature on a product. Query user's billing configuration getBillingConfigAsync() provides the country the user uses for Google Play. Note: Don't store any data that getBillingConfigAsync() returns; this data is designed to be used only once and can change at any time. You may not use getBillingConfigAsync() data to create or enhance a user profile or to target or track customers for advertising or marketing purposes. You can query the user's billing configuration after creating a BillingClient . The following code snippet describes how to make a call to getBillingConfigAsync() . Handle the response by implementing the BillingConfigResponseListener . This listener receives updates for all billing config queries initiated from your app. If the returned BillingResult contains no errors, you can then check the countryCode field in the BillingConfig object to obtain the user's Play Country. Note: The country code format is based on ISO-3166-1 alpha2 (UN country codes). See the Territory Containment table for more details. Kotlin // Use the default GetBillingConfigParams. val getBillingConfigParams = GetBillingConfigParams . newBuilder (). build () billingClient . getBillingConfigAsync ( getBillingConfigParams , object : BillingConfigResponseListener { override fun onBillingConfigResponse ( billingResult : BillingResult , billingConfig : BillingConfig? ) { if ( billingResult . responseCode == BillingResponseCode . OK && billingConfig != null ) { val countryCode = billingConfig . countryCode ... } else { // TODO: Handle errors } } }) Java // Use the default GetBillingConfigParams. GetBillingConfigParams getBillingConfigParams = GetBillingConfigParams . newBuilder (). build (); billingClient . getBillingConfigAsync ( getBillingConfigParams , new BillingConfigResponseListener () { public void onBillingConfigResponse ( BillingResult billingResult , BillingConfig billingConfig ) { if ( billingResult . getResponseCode () == BillingResponseCode . OK && billingConfig != null ) { String countryCode = billingConfig . getCountryCode (); ... } else { // TODO: Handle errors } } }); Cart abandonment reminders in Google Play Games home (enabled by default) For Games developers that monetize through one-time products, one way in which stock-keeping units (SKUs) that are active in Google Play Console can be sold outside of your app is the Cart Abandonment Reminder feature, which nudges users to complete their previously abandoned purchases while browsing the Google Play Store. These purchases happen outside of your app, from the Google Play Games home in the Google Play Store. This feature is enabled by default to help users pick up where they left off and to help developers maximize sales. However, you can opt your app out of this feature by submitting the Cart Abandonment Reminder feature opt-out form . For best practices on managing SKUs within the Google Play Console, see Create an in-app product . The following images show the Cart Abandonment Reminder appearing on the Google Play Store: Figure 2. The Google Play Store screen shows a purchase prompt for a previously abandoned purchase. Figure 3. The Google Play Store screen shows a purchase prompt for a previously abandoned purchase. Content and code samples on this page are subject to the licenses described in the Content License . Java and OpenJDK are trademarks or registered trademarks of Oracle and/or its affiliates. Last updated 2025-07-07 UTC. [[[\"Easy to understand\",\"easyToUnderstand\",\"thumb-up\"],[\"Solved my problem\",\"solvedMyProblem\",\"thumb-up\"],[\"Other\",\"otherUp\",\"thumb-up\"]],[[\"Missing the information I need\",\"missingTheInformationINeed\",\"thumb-down\"],[\"Too complicated / too many steps\",\"tooComplicatedTooManySteps\",\"thumb-down\"],[\"Out of date\",\"outOfDate\",\"thumb-down\"],[\"Samples / code issue\",\"samplesCodeIssue\",\"thumb-down\"],[\"Other\",\"otherDown\",\"thumb-down\"]],[\"Last updated 2025-07-07 UTC.\"],[],[]] X Follow @AndroidDev on X YouTube Check out Android Developers on YouTube LinkedIn Connect with the Android Developers community on LinkedIn More Android Android Android for Enterprise Security Source News Blog Podcasts Discover Gaming Machine Learning Health & Fitness Camera & Media Privacy 5G Android Devices Large screens Wear OS ChromeOS devices Android for cars Android TV Releases Android 15 Android 14 Android 13 Android 12 Android 11 Android 10 Pie Documentation and Downloads Android Studio guide Developers guides API reference Download Studio Android NDK Support Report platform bug Report documentation bug Google Play support Join research studies Android Chrome Firebase Google Cloud Platform All products Privacy License Brand guidelines Manage cookies Get news and tips by email Subscribe English Deutsch Español – América Latina Français Indonesia Italiano Polski Português – Brasil Tiếng Việt Türkçe Русский עברית العربيّة فارسی हिंदी বাংলা ภาษาไทย 中文 – 简体 中文 – 繁體 日本語 한국어"}, "https://developer.android.com/google/play/billing/subscription-with-addons": {"hash": "11b33a111c965eee7c25fa40793ae43d", "last_checked": "2025-07-18T17:46:53.321443", "content": "Subscription with add-ons | Google Play's billing system | Android Developers Skip to main content Essentials Build AI experiences Build AI-powered Android apps with Gemini APIs and more. Get started Get started Start by creating your first app. Go deeper with our training courses or explore app development on your own. Hello world Training courses Tutorials Compose for teams Kotlin for Android Monetization with Play ↗️ Extend by device Build apps that give your users seamless experiences from phones to tablets, watches, headsets, and more. Adaptive apps Android XR Wear OS Android for Cars Android TV ChromeOS Build by category Learn to build for your use case by following Google's prescriptive and opinionated guidance. Games Camera & media Social & messaging Health & fitness Productivity Enterprise apps Get the latest Stay in touch with the latest releases throughout the year, join our preview programs, and give us your feedback. Latest updates Experimental updates Android Studio preview Jetpack & Compose libraries Wear OS releases Privacy Sandbox ↗️ Design & Plan Excellent Experiences Build the best experiences for your best users. Learn more UI Design Design a beautiful user interface using Android best practices. Design for Android Mobile Adaptive UI Android XR Widgets Wear OS Android TV Architecture Design robust, testable, and maintainable app logic and services. Introduction Libraries Navigation Modularization Testing Quality Plan for app quality and align with Play store guidelines. Overview Core value User experience Accessibility Technical quality Excellent Experiences Security Safeguard users against threats and ensure a secure Android experience. Overview Privacy Permissions Identity Fraud prevention Develop Gemini in Android Studio Your AI development companion for Android development. Learn more Get Android Studio Core areas Get the samples and docs for the features you need. Samples User interfaces Background work Data and files Connectivity All core areas ⤵️ Tools and workflow Use the IDE to write and build your app, or create your own pipeline. Write and debug code Build projects Test your app Performance Command-line tools Gradle plugin API Device tech Write code for form factors. Connect devices and share data. Adaptive UI Wear OS Android XR Android Health Android for Cars Android TV All devices ⤵️ Libraries Browse API reference documentation with all the details. Android platform Jetpack libraries Compose libraries Google Play services ↗️ Google Play SDK index ↗️ Google Play Community / English Deutsch Español – América Latina Français Indonesia Italiano Polski Português – Brasil Tiếng Việt Türkçe Русский עברית العربيّة فارسی हिंदी বাংলা ภาษาไทย 中文 – 简体 中文 – 繁體 日本語 한국어 Android Studio Sign in Google Play Overview Play Console Play Billing Play Integrity Play Policies Play Services Brand & marketing Other Play guides Essentials More Design & Plan More Develop More Google Play Overview Play Console Play Billing Play Integrity Play Policies Play Services Brand & marketing Other Play guides Community Android Studio Sell digital content in apps Google Play's billing system About Google Play's billing system Setup Additional resources Play Billing Library Migrate to Billing Library 8 Play Billing Library reference Play Billing Library release notes Library version deprecation FAQ App integration Integrate the library Handle BillingResult response codes Server backend integration About server backend integration Purchase lifecycle and RTDNs One-time products About one-time products One-time product purchase lifecycle Multiple purchase options and offers Subscriptions About subscriptions Subscription with add-ons Subscription lifecycle Change subscription prices Manage subscriptions Google Play Developer API API reference Real-time developer notifications reference Play Developer API release notes API deprecations Best practices Manage your product catalog Run offers and promotions Fight fraud and abuse Developer Payload QueryPurchaseHistory Testing and troubleshooting Test your integration Test BillingResult response codes Monetizing outside of Google Play Billing Alternative Billing About alternative billing In-app integration with User Choice In-app integration without User Choice Interim UX guidelines User choice billing pilot Alternative in-app billing systems Alternative billing without user choice (EEA program) External offers About the program In-app integration Backend Integration Build AI experiences Get started Get started Hello world Training courses Tutorials Compose for teams Kotlin for Android Monetization with Play ↗️ Extend by device Adaptive apps Android XR Wear OS Android for Cars Android TV ChromeOS Build by category Games Camera & media Social & messaging Health & fitness Productivity Enterprise apps Get the latest Latest updates Experimental updates Android Studio preview Jetpack & Compose libraries Wear OS releases Privacy Sandbox ↗️ Excellent Experiences Learn more UI Design Design for Android Mobile Adaptive UI Android XR Widgets Wear OS Android TV Architecture Introduction Libraries Navigation Modularization Testing Quality Overview Core value User experience Accessibility Technical quality Excellent Experiences Security Overview Privacy Permissions Identity Fraud prevention Gemini in Android Studio Learn more Get Android Studio Core areas Samples User interfaces Background work Data and files Connectivity All core areas ⤵️ Tools and workflow Write and debug code Build projects Test your app Performance Command-line tools Gradle plugin API Device tech Adaptive UI Wear OS Android XR Android Health Android for Cars Android TV All devices ⤵️ Libraries Android platform Jetpack libraries Compose libraries Google Play services ↗️ Google Play SDK index ↗️ Reminder: By Aug 31, 2025, all new apps and updates to existing apps must use Billing Library version 7 or newer. If you need more time to update your app, you can request an extension until Nov 1, 2025. Learn about Play Billing Library version deprecation . Google Play's billing system Google Play Play Billing Subscription with add-ons Stay organized with collections Save and categorize content based on your preferences. Subscription with add-ons lets you bundle multiple subscription products together that can be purchased, billed and managed together. Your existing product catalog subscriptions can be seamlessly offered as add-ons without any upfront specification or additional configuration. You can launch a purchase flow with multiple existing subscription products, and sell them as add-ons. Considerations Consider the following points when using the subscription with add-ons feature: Subscription with add-ons is only supported for auto renewing base plans. All items in the purchase must have the same recurring billing period. For example, you can't have an annually billed subscription with monthly billed add-ons. You can have a maximum of 50 items in a subscription with add-ons purchase. This feature isn't available in the India ( IN ) and South Korea ( KR ) regions. Integrate with Play Billing Library This section describes how to integrate the subscription with add-ons feature with the Play Billing Library (PBL). It assumes that you are familiar with the initial PBL integration steps such as, adding the PBL dependency to your app , initializing the BillingClient , and connecting to Google Play . This section focuses on the PBL integration aspects that are specific to subscription with add-ons. Launch a purchase flow To launch a purchase flow for a subscription with add-ons, do the following steps: Fetch all your subscription items by using the BillingClient.queryProductDetailsAsync method. Set the ProductDetailsParams object for each item. The item represented by the ProductDetailsParams object, specifies both the ProductDetails indicating the subscription item, and an offerToken selecting a specific subscription base plan or offer . Note: All items in the ProductDetails must be from the same app. Specify the item details in the BillingFlowParams.Builder.setProductDetailsParamsList method. The BillingFlowParams class specifies the details of a purchase flow. Note: The first item in the list is referred to as the base item, and the remaining items are referred to as add-ons. And every item in this list must be unique. In other words, you can't set two ProductDetails with the same productId in the list. The following sample shows how to launch the billing flow for a subscription purchase with multiple items: Java BillingClient billingClient = … ; // ProductDetails obtained from queryProductDetailsAsync(). ProductDetailsParams productDetails1 = ...; ProductDetailsParams productDetails2 = ...; ArrayList productDetailsList = new ArrayList < > (); productDetailsList . add ( productDetails1 ); productDetailsList . add ( productDetails2 ); BillingFlowParams billingFlowParams = BillingFlowParams . newBuilder () . setProductDetailsParamsList ( productDetailsList ) . build (); billingClient . launchBillingFlow ( billingFlowParams ); Note: An in-app purchase will still be represented by the Purchase object. And the getProducts() method returns entitlements for all the items the user has from this purchase. Rules applicable for items in the purchase To ensure add-on renewal dates eventually align with the base item, Google Play may insert a prorated charge after any trial or intro pricing phases. Offer eligibility will be evaluated separately for each item. Process purchases Processing subscription with add-ons is the same as processing of single-item purchases as described in Integrate the Google Play Billing Library into your app . The only difference is that the user can receive multiple entitlements with a single purchase. A purchase of subscription with add-ons returns multiple items which can be retrieved using Purchase.getProducts() in the Google Play Billing Library, and then the lineItems list in purchases.subscriptionsv2.get of the Google Play Developer API . Note: For each call of the launchBillingFlow API, there is a limit of 50 items (active items + new items). If the cart contains more than 50 items, Google Play blocks the purchase flow. Modify subscriptions with add-ons Any changes to your subscription with add-ons, results in an upgrade or a downgrade. For more information, see upgrade or downgrade subscriptions . To change or restore an existing purchase of subscription with add-ons in your app, you must call the launchBillingFlow API with additional parameters, and ensure the following: Always call setOldPurchaseToken with the purchase token of the current subscription purchase. To upgrade, downgrade, or crossgrade the base item, call setSubscriptionReplacementMode to specify how the plan change should be handled between the base items of the old, and the new purchase of subscription with add-ons. Otherwise, there is no need to set this parameter. When the base item isn't changed, you can still call setSubscriptionReplacementMode to apply a specific proration behavior. For the applicable rules in this case, see Resubscribe or switch plans within the same subscription . New add-ons will apply immediately with a prorated charge to align the next renewal date with the base item in the subscription. Removed add-ons will expire at the end of their current billing periods. When launching the billing flow, you will need to specify all active items in the subscription with add-ons excluding those to be removed, along with any new add-ons. The following sample shows how to call the launchBillingFlow API when changing an existing purchase of subscription with add-ons: Java BillingClient billingClient = … ; int replacementMode = … ; // ProductDetails obtained from queryProductDetailsAsync(). ProductDetailsParams productDetails1 = ...; ProductDetailsParams productDetails2 = ...; ProductDetailsParams productDetails3 = ...; ArrayList newProductDetailsList = new ArrayList < > (); newProductDetailsList . add ( productDetails1 ); newProductDetailsList . add ( productDetails1 ); newProductDetailsList . add ( productDetails1 ); BillingFlowParams billingFlowParams = BillingFlowParams . newBuilder () . setSubscriptionUpdateParams ( SubscriptionUpdateParams . newBuilder () . setOldPurchaseToken ( purchaseTokenOfExistingSubscription ) // No need to set if change does not affect the base item. . setSubscriptionReplacementMode ( replacementMode ) . build ()) . setProductDetailsParamsList ( productDetailsList ) . build (); billingClient . launchBillingFlow ( billingFlowParams ); Subscription modification scenarios The following table lists the various modification scenarios for subscription with add-ons, and the corresponding behaviour. Existing items Modified items Do you need to set the replacement mode? Behavior A (base item), B A (base item) No Item B is scheduled for deferred removal. A A (base item), B No Item B is added immediately with a prorated charge. A (base item), B A (base item), C No B is scheduled for deferred removal. C is added immediately with a prorated charge. A (base item), B B (base item) No A is scheduled for a deferred removal. A (base item), B C (base item) Yes The replacement for A -> C depends on setSubscriptionReplacementMode . B is scheduled for deferred removal. A (base item), B C (base item), B Yes The replacement for A -> C depends on setSubscriptionReplacementMode . A (base item), B C (base item), D Yes The replacement for A -> C depends on setSubscriptionReplacementMode . B is scheduled for deferred removal. D is added immediately with a prorated charge. Real-time developer notifications The subscriptionId field isn't provided in RTDN for purchases of subscription with add-ons, which contain multiple item entitlements. Instead, your can use the Play Developer APIs to get the purchase and see the associated item entitlements. Price changes for existing subscribers Changing subscription prices for existing subscribers of a subscription with add-ons purchase is similar to changing the subscription prices of single-item subscriptions as described in Change subscription prices . However, there are some limitations and functional differences as described in this section. End a legacy price cohort Ending a legacy cohort also impacts subscription with add-on purchases. The following rules apply: All outstanding opt-in price increases should have the same renewal time with the new price. If an item in a subscription with add-ons purchase has an opt-in price increase which is not yet confirmed by the user, any new opt-in price increase for other items in the purchase will be ignored unless it results in the same renewal time of new price application as the existing price increase in OUTSTANDING state. Once the user confirms the price increase any newer price changes would be registered. And users can only accept all unconfirmed opt-in price increases at once. Example: Consider a subscription with add-ons (items A and B), renews on 7th of every month. Item A has an ongoing price migration from $7 to $10, and the price increase is expected to be applicable on July 7. A new price migration from $5 to $6, starts for item B on June 2. Since the opt-in price increase starts 37 days after migration, for earliest price increase for item B will be on Aug 7. In this scenario, until the user accepts the price change for item A (until it is in CONFIRMED state), price change for item B isn't registered for this subscription purchase, and SubscriptionPurchaseV2 doesn't return price change details for item B. After the user confirms the price change for item A, the price change of item B starts. The user receives the item B opt-in price increase only after accepting the opt-in increase for item A. Google Play's email contains a list of all the items with price increases or decreases taking effect on the same day. Cancel subscription with add-ons Users can cancel the entire purchase of a subscription with add-ons on Play Subscription Center, and you can only cancel the entire purchase of a subscription with add-ons by using the Google Play Developer API. When a subscription purchase is canceled without being revoked, none of the items in the purchase will auto-renew, but the user will continue to have access to the entitled items until the corresponding billing periods end. Revoke and refund subscriptions with add-ons The following are some of the guidelines for revoking and refunding subscriptions: Use Play Console to issue an amount based refund for a specific Order without revoking access to the subscription. Call orders.refund to fully refund specific subscription payments the user has made without revoking access to the subscription. Note: Don't call purchases.subscriptions.refund (deprecated) for a subscription with add-ons. Call purchases.subscriptionsv2.revoke to immediately revoke access to all subscription items. With this API, you can: Revoke access to all the items and provide a prorated refund. When revoking a subscription with add-ons using prorated refunds, a refund will be issued for the latest order of each item with a prorated amount based on the time remaining until the next renewal. Revoke access for all the items and provide a FullRefund . Revoke individual item's access with full refund on the item. Revoke individual item in a subscription with add-ons To revoke individual subscription items in a subscription with add-ons without revoking the entire purchase, call purchases.subscriptionsv2.revoke with ItemBasedRefund field set in the RevocationContext . The productId of the item that should be revoked and refunded can be set in the ItemBasedRefund field. The ItemBasedRefund field can be set for purchases with one or more auto-renewing subscription items. If there are still active items remaining in the subscription purchase after revoking the item specified in ItemBasedRefund , only the item will be revoked, and fully refunded without interrupting the subscription status. If there are no active items remaining in the subscription purchase after revoking the item specified in ItemBasedRefund , the item is revoked, fully refunded, and the subscription is canceled. Considerations When using the ItemBasedRefund only one item can be revoked at a time. The request can be called multiple times if different items need to be revoked. When the subscription purchase is in any of the payment declined states, or the item specified in ItemBasedRefund is not owned or expired, the item turn down is blocked. Item turn down isn't supported in prepaid subscription. Item expiration during payment decline For a purchase of subscription with add-ons, certain renewals may only need to extend a subset of item entitlements, without affecting items with a future expiry date. Regardless of which items are involved in a renewal, if the renewal payment is declined, the overall subscription purchase will enter grace period and account hold as described in the following documentation. Recovery period selection As grace period itself still grants the user entitlement, upon a purchase of subscription with add-ons, renewal payment is declined, the item with the minimum grace period over all active items is selected, and its grace period and account hold period as the recovery period is applied for this renewal. Active items includes items that were active in the purchase of a subscription with add-ons just prior to the renewal attempt, excludes any newly added items (which won't be entitled until after recovery), and excludes any items that are no longer active due to removal or turndown. The account hold setting of the item with the minimum grace period selected is applied. If there are more than one items with the minimum grace period, but different account hold periods, the longest account hold period is applied. Grace period When a subscription renewal payment is declined, the subscription purchase will enter grace period state. During the grace period, the user will continue to have access to all active items from the previous renewal period. After the grace period, if the payment method hasn't been fixed, the entire subscription purchase goes into account hold. If any other items reach their renewal date during grace period, a new charge attempt will be initiated for those items once the subscription recovers from payment decline. Account hold While the subscription purchase is in account hold, access to all subscription items is suspended until payment recovers. If the subscription in account hold is recovered, the subscription purchase continues existing as is. If the subscription is not recovered, the items in payment decline will expire, and access to the other items will be resumed for the remainder of their billing periods. Example: A user has a subscription My Base Plan renewing at the 1st of every month, then on Aug 15, adds a $10 per month Add on plan with a seven-day free trial. Neither of the items have grace period set, and they both have 30-day account hold period. On the Aug 22, the user is charged $2.90 (10*9/31) to prorate until Aug 31, but the user's payment method expires before then, and the subscription falls into payment decline on Aug 22. When the subscription enters account hold due to payment decline, the user does not have access to any of the items in the subscription with add-ons. The remaining time for the items that are not being renewed will be given back to users when the subscription exits account hold, either because payment has been recovered or canceled. In the previous example, a subscription enters account hold on Aug 22. If the account is recovered on Aug 25, before the broader renewal date on Sep 1, the user regains access to both My Base Plan , and Add on plan the same day. Next billing date is changed to Sep 4. If the account isn't recovered after 30 days, the subscription is canceled on Sep 21 and the user loses access to the Add on plan , and resume access to My Base Plan until Sep 30. In this example, you must get the updated expiryTime for ALL items in the subscription with add-ons, as some items may resume their entitlement after the grace period and account hold. Financial reporting and reconciliation Use the Earnings report to reconcile your active subscriptions with transactions on Play. Each transaction line item has an order ID. With purchases representing several items, the Earnings and Estimated sales reports will include separate rows for each transaction such as charge, fee, tax, and refund, for each item involved. For dashboards in the Play Console: The revenue statistics presented in the Financial reporting section of the console are broken down by items. Order management reflects purchase of subscription with add-ons, and show itemized lists of what was purchased. From order management, you may revoke, cancel or fully refund a user's purchase. Content and code samples on this page are subject to the licenses described in the Content License . Java and OpenJDK are trademarks or registered trademarks of Oracle and/or its affiliates. Last updated 2025-07-07 UTC. [[[\"Easy to understand\",\"easyToUnderstand\",\"thumb-up\"],[\"Solved my problem\",\"solvedMyProblem\",\"thumb-up\"],[\"Other\",\"otherUp\",\"thumb-up\"]],[[\"Missing the information I need\",\"missingTheInformationINeed\",\"thumb-down\"],[\"Too complicated / too many steps\",\"tooComplicatedTooManySteps\",\"thumb-down\"],[\"Out of date\",\"outOfDate\",\"thumb-down\"],[\"Samples / code issue\",\"samplesCodeIssue\",\"thumb-down\"],[\"Other\",\"otherDown\",\"thumb-down\"]],[\"Last updated 2025-07-07 UTC.\"],[],[]] X Follow @AndroidDev on X YouTube Check out Android Developers on YouTube LinkedIn Connect with the Android Developers community on LinkedIn More Android Android Android for Enterprise Security Source News Blog Podcasts Discover Gaming Machine Learning Health & Fitness Camera & Media Privacy 5G Android Devices Large screens Wear OS ChromeOS devices Android for cars Android TV Releases Android 15 Android 14 Android 13 Android 12 Android 11 Android 10 Pie Documentation and Downloads Android Studio guide Developers guides API reference Download Studio Android NDK Support Report platform bug Report documentation bug Google Play support Join research studies Android Chrome Firebase Google Cloud Platform All products Privacy License Brand guidelines Manage cookies Get news and tips by email Subscribe English Deutsch Español – América Latina Français Indonesia Italiano Polski Português – Brasil Tiếng Việt Türkçe Русский עברית العربيّة فارسی हिंदी বাংলা ภาษาไทย 中文 – 简体 中文 – 繁體 日本語 한국어"}, "https://developer.android.com/google/play/billing/lifecycle/subscriptions": {"hash": "7a4f233cb550d64442b1bec7325acfab", "last_checked": "2025-07-18T17:46:57.145223", "content": "Subscription lifecycle | Google Play's billing system | Android Developers Skip to main content Essentials Build AI experiences Build AI-powered Android apps with Gemini APIs and more. Get started Get started Start by creating your first app. Go deeper with our training courses or explore app development on your own. Hello world Training courses Tutorials Compose for teams Kotlin for Android Monetization with Play ↗️ Extend by device Build apps that give your users seamless experiences from phones to tablets, watches, headsets, and more. Adaptive apps Android XR Wear OS Android for Cars Android TV ChromeOS Build by category Learn to build for your use case by following Google's prescriptive and opinionated guidance. Games Camera & media Social & messaging Health & fitness Productivity Enterprise apps Get the latest Stay in touch with the latest releases throughout the year, join our preview programs, and give us your feedback. Latest updates Experimental updates Android Studio preview Jetpack & Compose libraries Wear OS releases Privacy Sandbox ↗️ Design & Plan Excellent Experiences Build the best experiences for your best users. Learn more UI Design Design a beautiful user interface using Android best practices. Design for Android Mobile Adaptive UI Android XR Widgets Wear OS Android TV Architecture Design robust, testable, and maintainable app logic and services. Introduction Libraries Navigation Modularization Testing Quality Plan for app quality and align with Play store guidelines. Overview Core value User experience Accessibility Technical quality Excellent Experiences Security Safeguard users against threats and ensure a secure Android experience. Overview Privacy Permissions Identity Fraud prevention Develop Gemini in Android Studio Your AI development companion for Android development. Learn more Get Android Studio Core areas Get the samples and docs for the features you need. Samples User interfaces Background work Data and files Connectivity All core areas ⤵️ Tools and workflow Use the IDE to write and build your app, or create your own pipeline. Write and debug code Build projects Test your app Performance Command-line tools Gradle plugin API Device tech Write code for form factors. Connect devices and share data. Adaptive UI Wear OS Android XR Android Health Android for Cars Android TV All devices ⤵️ Libraries Browse API reference documentation with all the details. Android platform Jetpack libraries Compose libraries Google Play services ↗️ Google Play SDK index ↗️ Google Play Community / English Deutsch Español – América Latina Français Indonesia Italiano Polski Português – Brasil Tiếng Việt Türkçe Русский עברית العربيّة فارسی हिंदी বাংলা ภาษาไทย 中文 – 简体 中文 – 繁體 日本語 한국어 Android Studio Sign in Google Play Overview Play Console Play Billing Play Integrity Play Policies Play Services Brand & marketing Other Play guides Essentials More Design & Plan More Develop More Google Play Overview Play Console Play Billing Play Integrity Play Policies Play Services Brand & marketing Other Play guides Community Android Studio Sell digital content in apps Google Play's billing system About Google Play's billing system Setup Additional resources Play Billing Library Migrate to Billing Library 8 Play Billing Library reference Play Billing Library release notes Library version deprecation FAQ App integration Integrate the library Handle BillingResult response codes Server backend integration About server backend integration Purchase lifecycle and RTDNs One-time products About one-time products One-time product purchase lifecycle Multiple purchase options and offers Subscriptions About subscriptions Subscription with add-ons Subscription lifecycle Change subscription prices Manage subscriptions Google Play Developer API API reference Real-time developer notifications reference Play Developer API release notes API deprecations Best practices Manage your product catalog Run offers and promotions Fight fraud and abuse Developer Payload QueryPurchaseHistory Testing and troubleshooting Test your integration Test BillingResult response codes Monetizing outside of Google Play Billing Alternative Billing About alternative billing In-app integration with User Choice In-app integration without User Choice Interim UX guidelines User choice billing pilot Alternative in-app billing systems Alternative billing without user choice (EEA program) External offers About the program In-app integration Backend Integration Build AI experiences Get started Get started Hello world Training courses Tutorials Compose for teams Kotlin for Android Monetization with Play ↗️ Extend by device Adaptive apps Android XR Wear OS Android for Cars Android TV ChromeOS Build by category Games Camera & media Social & messaging Health & fitness Productivity Enterprise apps Get the latest Latest updates Experimental updates Android Studio preview Jetpack & Compose libraries Wear OS releases Privacy Sandbox ↗️ Excellent Experiences Learn more UI Design Design for Android Mobile Adaptive UI Android XR Widgets Wear OS Android TV Architecture Introduction Libraries Navigation Modularization Testing Quality Overview Core value User experience Accessibility Technical quality Excellent Experiences Security Overview Privacy Permissions Identity Fraud prevention Gemini in Android Studio Learn more Get Android Studio Core areas Samples User interfaces Background work Data and files Connectivity All core areas ⤵️ Tools and workflow Write and debug code Build projects Test your app Performance Command-line tools Gradle plugin API Device tech Adaptive UI Wear OS Android XR Android Health Android for Cars Android TV All devices ⤵️ Libraries Android platform Jetpack libraries Compose libraries Google Play services ↗️ Google Play SDK index ↗️ Reminder: By Aug 31, 2025, all new apps and updates to existing apps must use Billing Library version 7 or newer. If you need more time to update your app, you can request an extension until Nov 1, 2025. Learn about Play Billing Library version deprecation . Google Play's billing system Google Play Play Billing Subscription lifecycle Stay organized with collections Save and categorize content based on your preferences. Subscription purchases can go through several different states throughout their lifecycle, depending on many factors including auto-renewal behavior, payment decline situations, and developer management actions. Handle lifecycle for auto-renewing subscriptions When a user's subscription state changes, your backend server receives a SubscriptionNotification message. Figure 1. Lifecycle states and transition events for auto-renewing subscription purchases. To update the state in your backend, call the purchases.subscriptionsv2.get API with the purchase token included in the notification. This endpoint provides the latest subscription state given a purchase token and is considered the source of truth for subscription management. The purchase token is valid from subscription signup until 60 days after expiration. After this date, the purchase token is no longer valid to use to call the Google Play Developer API. Note: The purchases.subscriptions.get method is deprecated and present for backward compatibility reasons. This method shouldn't be used to obtain subscription states for new integrations. If you have an existing integration that uses this method, see API deprecations for alternatives. Other methods in the purchases.subscriptions endpoint are still in use. New auto-renewing subscription purchases When a user purchases a subscription, a SubscriptionNotification message with type SUBSCRIPTION_PURCHASED is sent to your RTDN client. Whether you receive this notification or you register a new purchase in-app through PurchasesUpdatedListener or manually fetching purchases in your app's onResume() method, you should process the new purchase in your secure backend. To do this, follow these steps: Query the purchases.subscriptionsv2.get endpoint to get a subscription resource that contains the latest subscription state. Make sure that the value of the subscriptionState field is SUBSCRIPTION_STATE_ACTIVE . Verify the purchase . Give the user access to the content. The user account associated with the purchase can be identified with the ExternalAccountIdentifiers object from the subscription resource if identifiers were set at purchase time using setObfuscatedAccountId and setObfuscatedProfileId . Note: If you don't acknowledge a new subscription purchase within three days, the user automatically receives a refund, and Google Play revokes the purchase. The Play Billing Library also includes a method to acknowledge a subscription, acknowledgePurchase() , and a method to check acknowledgement status, isAcknowledged() . However, we recommend that you handle purchase processing in your backend for better security. The subscription resource for new purchases looks similar to the following example: { \"kind\" : \"androidpublisher#subscriptionPurchaseV2\" , \"startTime\" : \"2022-04-22T18:39:58.270Z\" , \"regionCode\" : \"US\" , \"subscriptionState\" : \"SUBSCRIPTION_STATE_ACTIVE\" , \"latestOrderId\" : \"GPA.3333-4137-0319-36762\" , \"acknowledgementState\" : \"ACKNOWLEDGEMENT_STATE_PENDING\" , // need to acknowledge new purchases \"lineItems\" : [ { \"productId\" : \"sub_variant_plan01\" , \"expiryTime\" : ne x t _re ne wal_da te , \"autoRenewingPlan\" : { \"autoRenewEnabled\" : true } } ], } Subscription renewals For non-installment, auto-renewing subscriptions, a SUBSCRIPTION_RENEWED notification is sent when the subscription renews. For installment subscriptions, a SUBSCRIPTION_RENEWED notification is sent each time the subscription is charged on its billing date. Make sure that the user is still entitled to the subscription and then update the subscription state with the new expiryTime provided in the subscription resource returned from the Google Play Developer API. The subscription resource looks similar to the following example: { \"kind\" : \"androidpublisher#subscriptionPurchaseV2\" , \"startTime\" : \"2022-04-22T18:39:58.270Z\" , \"regionCode\" : \"US\" , \"subscriptionState\" : \"SUBSCRIPTION_STATE_ACTIVE\" , \"latestOrderId\" : \"GPA.3333-4137-0319-36762\" , \"acknowledgementState\" : \"ACKNOWLEDGEMENT_STATE_ACKNOWLEDGED\" , \"lineItems\" : [ { \"productId\" : \"sub_variant_plan01\" , \"expiryTime\" : ne x t _re ne wal_da te , \"autoRenewingPlan\" : { \"autoRenewEnabled\" : true } } ] } You don't need to acknowledge subscription renewals. Note: If a subscription is set to renew on the 29th, 30th, or 31st of the month going into February of a non-leap year, then the subscription renewal day is moved to the 28th of February and continues to renew on the 28th of each month for the duration of the subscription. Similarly, if a user starts a subscription on March 31st, the subscription renews on April 30th and continues to renew on the 30th of each month. Grace period Note: By default, all auto-renewing base plans have grace period enabled. You can adjust the grace period length or disable it from the Google Play Console . Specifying a length less than the default value may reduce the number of subscriptions recovered from payment declines. If there are payment issues with a subscription renewal, Google notifies the user and periodically attempts to renew the subscription for some time before the subscription expires. This recovery period can consist of a grace period followed by an account hold period. During a grace period, the user should still have access to their subscription entitlement. The queryPurchasesAsync() method continues to return purchases that are in the grace period. If your app relies solely on queryPurchasesAsync to check whether a user is entitled to a subscription, then your app should automatically handle grace periods, because these subscriptions are shown as active through the Play Billing Library. Synchronizing subscription state with your backend lets you to be more aware of payment declines and gives you more context as you try to reduce involuntary churn. Listen for SubscriptionNotification messages with type SUBSCRIPTION_IN_GRACE_PERIOD to be notified when the user enters a grace period. While the user is in a grace period, the subscription resource contains autoRenewEnabled = true . Google Play dynamically extends the expiryTime value until the grace period has expired because entitlement should last until the user cancels or the grace period has lasted for its maximum length. The value of the subscriptionState field during this period is SUBSCRIPTION_STATE_IN_GRACE_PERIOD . The subscription resource looks similar to the following example: { \"kind\" : \"androidpublisher#subscriptionPurchaseV2\" , ... \"subscriptionState\" : \"SUBSCRIPTION_STATE_IN_GRACE_PERIOD\" , ... \"lineItems\" : [ { \"productId\" : \"sub_variant_plan01\" , \"expiryTime\" : t imes ta mp_i n _ future , \"autoRenewingPlan\" : { \"autoRenewEnabled\" : true } } ], } Play informs users that are in a grace period that their payment was declined and prompts them to fix their payment method issues in the Play Store. When a user enters a grace period, you should also encourage the user to fix their payment method in case the failure was involuntary. A straightforward way to do this is to use the In-App Messaging API . If you call this API when the user opens your app, they are shown a Play message in a temporary snackbar informing the user that their payment has been declined. This message also includes a deep link for the user to fix their payment method on Google Play. As soon as the user fixes their payment method, the subscription renews with its original renewal date, and you can handle the renewal as described in Renewals . If the user does not fix their payment method during the grace period, the subscription enters account hold , and they lose entitlement. Grace period access and recovery Figure 2 shows a timeline for a subscription that enters into a grace period and then recovers when the user fixes their payment method. After the grace period ends, the user should lose subscription benefits and go into account hold. Figure 2. Timeline for a subscription that enters a grace period and recovers before it ends. It is important to remember the following points: During a grace period, the user should retain access to subscription benefits. When a subscription recovers during a grace period, the renewal date does not reset. If you increase the grace period—for example, from 7 days to 14 days—users who are in a grace period get extended access to subscription benefits. If you decrease the grace period, users who are far enough into the old grace period to exceed the new grace period have their subscription benefits revoked immediately. For example, if you decrease the grace period from 14 days to 7 days, users who are in days 8-14 of the old grace period have their subscription benefits revoked immediately. The subscription remains in an active state and you won't receive a grace period RTDN until the silent grace period ends Silent grace period You can set a grace period of 0 days, but Play will wait a minimum of 1 day to ensure sufficient time for payment retries. This silent grace period offers a safety net for payment processing. During this 24‑hour period the subscription remains in the ACTIVE state . The best way for you to stay in sync with subscription state changes is to listen and react to the real-time developer notifications (RTDN). Call the purchases.subscriptionsv2.get() method at the RTDN time instead of the expiry time to get a more accurate status of the subscription. Depending on the subscription status after the 24‑hour silent grace period, you should receive one of the following notifications: SUBSCRIPTION_ON_HOLD (if enabled) SUBSCRIPTION_CANCELED (if canceled) SUBSCRIPTION_EXPIRED (if expired) SUBSCRIPTION_RENEWED (if successfully renewed) You can also call the subscriptionV2.get() method at any point after the 24‑hour silent grace period to get the latest status of the subscription. Account hold Note: By default, all auto-renewing base plans have account hold enabled. You can adjust the account hold length or disable it from the Google Play Console . Specifying a length less than the default value may reduce the number of subscriptions recovered from payment declines. If there are payment issues with a subscription renewal, after any grace period has ended, an account hold period begins. When a subscription enters account hold, you should block access to the subscription entitlement. During account hold, you should continue to handle any cancellations , restorations, or repurchases of your subscriptions as needed, because it's possible for the user to make these changes while the subscription is on hold. RTDNs notify you when the user enters the account hold period, so you can inform them as soon as possible of why their access to the subscription was suspended. A straightforward way to do this is to use the In-App Messaging API . Calling this API when your user opens the app will show the user a message in a temporary snackbar informing them that their payment has been declined. This message also includes a deep link for the user to fix their payment method on Google Play. If your users can access subscription content outside of your app, they might discover that they have lost access on different surfaces. You might want to send a push notification or an email to the user to let them know that their subscription is no longer active due to payment decline. The subscription is not returned by the queryPurchasesAsync() method during account hold, so if your app relies on this method to display existing purchases, you should support account hold by default. With real-time developer notifications , you receive a SubscriptionNotification message with type SUBSCRIPTION_ON_HOLD when a subscription enters account hold. Call the purchases.subscriptionsv2.get method from your secure backend server to retrieve the new subscription information. During account hold the expiryTime field of the subscription resource is set to a past timestamp, and the subscriptionState field is set to SUBSCRIPTION_STATE_ON_HOLD : { \"kind\" : \"androidpublisher#subscriptionPurchaseV2\" , ... \"subscriptionState\" : \"SUBSCRIPTION_STATE_ON_HOLD\" , ... \"lineItems\" : [ { \"productId\" : \"sub_variant_plan01\" , \"expiryTime\" : t imes ta mp_i n _pas t , ... } ], } To restore access, users must fix their payment method. Play informs users in account hold of their payment decline, and you should also encourage them to fix their payment method. After the user fixes their payment method, the subscription returns to an active state, and you must then restore access to the subscribed content. In this case, the purchase token is the same as it was before the account hold started because the same purchase is recovering, and you receive an RTDN with type SUBSCRIPTION_RECOVERED . For installment subscriptions, payment declines and recoveries could occur for any individual payment attempt. Note: If a subscription is recovered from account hold, the billing date moves to the date of recovery. After recovery, the Play Billing Library returns the subscription again through the queryPurchasesAsync() method. If you use this method to determine whether a user is entitled to a subscription, then your app should automatically handle the subscription recovering from account hold. Listen for a SubscriptionNotification message with type SUBSCRIPTION_RECOVERED to be notified when a subscription is recovered and the user should regain access. If you query for a subscription after receiving this notification, the expiryTime field is set to a timestamp in the future and the subscriptionState field is set to SUBSCRIPTION_STATE_ACTIVE again: { \"kind\" : \"androidpublisher#subscriptionPurchaseV2\" , ... \"subscriptionState\" : \"SUBSCRIPTION_STATE_ACTIVE\" , ... \"lineItems\" : [ { \"productId\" : \"sub_variant_plan01\" , \"expiryTime\" : ne x t _re ne wal_da te , ... } ], } If the user does not fix their payment method before the end of the account hold period, you instead receive an RTDN with type SUBSCRIPTION_CANCELED . For instructions on handling a cancellation, see Cancellations . When you query for a subscription that was canceled in this way, the returned expiryTime field is set to a past timestamp: { \"kind\" : \"androidpublisher#subscriptionPurchaseV2\" , ... \"subscriptionState\" : \"SUBSCRIPTION_STATE_CANCELED\" , ... \"lineItems\" : [ { \"productId\" : \"sub_variant_plan01\" , \"expiryTime\" : t imes ta mp_i n _pas t , ... } ], } Immediately after you are notified of the cancellation during account hold, you will also receive an RTDN with type SUBSCRIPTION_EXPIRED because the user is out of paid entitlement and the subscription has churned with the cancellation. You can handle this expiration the way you normally would. The user can regain access by repurchasing the same subscription plan or any other plan that you offer through the app during their account hold period from their original purchase. In that case, a new purchase token is issued and the new value is returned as part of a SUBSCRIPTION_PURCHASED event that represents this new instance. Account hold access and recovery Figure 3 shows a timeline for a subscription that enters into account hold and then recovers when the user fixes their payment method. Figure 3. Timeline for a subscription that enters an account hold and recovers before it ends. Similar to the previous example, Figure 4 shows a timeline for a subscription that first enters into a grace period before entering account hold, and then recovers while on hold. Figure 4. Timeline for a subscription that enters a grace period, then enters account hold, and finally recovers before the account hold ends. It is important to remember the following points: Before a subscription enters into account hold, Google Play makes additional attempts to charge the payment method for up to 48 hours. The user retains subscription benefits during this period. After this retry period has elapsed, the subscription then enters into account hold, and the user should lose access to subscription benefits. The subscription enters into account hold directly when the subscription resumes from a paused state with a failed form of payment. When a subscription recovers from account hold, the renewal date resets. Expirations Once a subscription expires, the user should lose access to the subscription. A SubscriptionNotification message with type SUBSCRIPTION_EXPIRED is sent in that case. When you receive this notification, query the Google Play Developer API to get the latest subscription resource . After you confirm that the subscriptionState is SUBSCRIPTION_STATE_EXPIRED , remove the entitlement and register the purchase state as invalid in your backend. The subscription resource looks similar to the following example: { \"kind\" : \"androidpublisher#subscriptionPurchaseV2\" , ... \"subscriptionState\" : \"SUBSCRIPTION_STATE_EXPIRED\" , ... \"lineItems\" : [ { \"productId\" : \"sub_variant_plan01\" , \"expiryTime\" : expira t io n _ t ime_i n _pas t , ... } ], } Cancellations A user can voluntarily cancel a subscription from the Play subscriptions center or have their subscription automatically canceled if they don't recover after being in account hold . Developers can also trigger a cancellation with purchases.subscriptions.cancel When a subscription is canceled, the user retains access to the content until the end of the current billing cycle. When the billing cycle ends, access should be revoked. Note: For installment subscriptions, user-initiated cancellation takes effect at the end of the current commitment period. Developer-initiated cancellation, using the purchases.subscriptions.cancel API, where the cancellationType request parameter is set to USER_REQUESTED_STOP_RENEWAL , takes effect at the end of the current commitment period. However, setting the cancellationType request parameter to DEVELOPER_REQUESTED_STOP_PAYMENTS , stops the next payment. Canceling a non-installment, auto-renewing subscription triggers a SUBSCRIPTION_CANCELED notification. When you receive this notification, the subscription resource returned from the Google Play Developer API has the subscriptionState field set to SUBSCRIPTION_STATE_CANCELED , and the expiryTime field contains the date when the user should lose access to the subscription. If that date is in the past, then the user should lose entitlement immediately. This could happen, for example, if a user cancels a subscription while on account hold due to a payment decline. The subscription resource for a canceled purchase looks similar to the following example: { \"kind\" : \"androidpublisher#subscriptionPurchaseV2\" , ... \"subscriptionState\" : \"SUBSCRIPTION_STATE_CANCELED\" , ... \"lineItems\" : [ { \"productId\" : \"sub_variant_plan01\" , \"expiryTime\" : expira t io n _ t ime , ... } ], } For installment subscriptions, a SUBSCRIPTION_CANCELLATION_SCHEDULED notification gets sent upon a user-initiated cancellation when payments remain for the commitment period. The cancellation is pending and takes effect at the end of the current commitment period. When you receive this notification, the subscription resource returned from the Google Play Developer API has the subscriptionState field set to SUBSCRIPTION_STATE_ACTIVE because the installment subscription is still active until the end of the commitment period. However, there is an empty pendingCancellation object present. A SUBSCRIPTION_CANCELED notification gets sent followed by a SUBSCRIPTION_EXPIRED at the end of the commitment period. The subscription resource for a installment subscription purchase that is pending cancellation looks similar to the following example: { \"kind\" : \"androidpublisher#subscriptionPurchaseV2\" , ... \"subscriptionState\" : \"SUBSCRIPTION_STATE_ACTIVE\" , ... \"lineItems\" : [ { \"productId\" : \"sub_plan01\" , \"expiryTime\" : expira t io n _ t ime , \"autoRenewingPlan\" : { \"autoRenewEnabled\" : true , \"recurringPrice\" : { \"currencyCode\" : \"USD\" , \"units\" : \"1\" , \"nanos\" : 990000000 }, \"installmentDetails\" : { \"initialCommittedPaymentsCount\" : 6 , \"remainingCommittedPaymentsCount\" : 5 , \"pendingCancellation\" : {} ... } } } ], } You can look at the canceledStateContext field in the subscription resource to learn why the subscription was canceled (for example, whether the subscription was canceled by the user, by the system, or by you). If the subscription was canceled by the user, you can look at the userInitiatedCancellation field to learn why the user canceled the subscription. This can help inform communication strategies. When a subscription is canceled but has not yet expired , it is still returned from queryPurchasesAsync() . You might want to display a message in your app informing the user that their subscription was canceled and giving them the date of expiration. Warning: Don't remove access to a subscription from Google Play while the user is still entitled to the content. Removing access to content that a user is entitled to is a violation of Google Play's subscriptions policy . Revocations A subscription can be revoked for a variety of reasons, including your backend revoking the subscription by using purchases.subscriptionsv2.revoke or the purchase being charged back. In this situation, revoke the user's entitlement immediately. A SubscriptionNotification message with type SUBSCRIPTION_REVOKED is sent when this occurs. When you receive this notification, the subscription resource returned from the Google Play Developer API has the subscriptionState field set to SUBSCRIPTION_STATE_EXPIRED . The subscription resource for a revoked purchase looks similar to the following example: { \"kind\" : \"androidpublisher#subscriptionPurchaseV2\" , ... \"subscriptionState\" : \"SUBSCRIPTION_STATE_EXPIRED\" , ... \"lineItems\" : [ { \"productId\" : \"sub_variant_plan01\" , \"expiryTime\" : expira t io n _ t ime , ... } ] } Deferred subscriptions There are a variety of reasons why you might want to extend a user's entitlement. For example, you might want to offer users free access as a special promotion, such as giving one week free for purchasing a movie or providing free access to customers as a gesture of goodwill. You can use the purchases.subscriptions.defer method from the Play Developer API to advance the next bililng date for an auto-renewing subscription. When you do this, a SubscriptionNotification message with type SUBSCRIPTION_DEFERRED is sent. During the deferral period, the user is subscribed to your content with full access but is not charged. The subscription renewal date is updated to reflect the new date. For prepaid plans, you can use the defer billing API to defer the expiration time. The subscription resource for a deferred subscription looks similar to the following example: { \"kind\" : \"androidpublisher#subscriptionPurchaseV2\" , ... \"subscriptionState\" : \"SUBSCRIPTION_STATE_ACTIVE\" , ... \"lineItems\" : [ { \"productId\" : \"sub_variant_plan01\" , \"expiryTime\" : t imes ta mp_i n _ future , ... } ], } Paused subscriptions Note: All subscriptions have pause enabled by default. You can disable pause from the Google Play Console . You can reduce voluntary churn by enabling users to pause their subscription. When you enable the pause feature, users can choose to pause their subscription for a period of time between one week and three months, depending on the recurring period. Subscription recurrence Weekly Monthly Three-month Six-month Annual Available pause lengths * 1 week 2 weeks 3 weeks 4 weeks 1 month 2 months 3 months 1 month 2 months 3 months 1 month 2 months 3 months N/A * Subject to change at any time. A subscription pause takes effect only after the current billing period ends. While the subscription is paused, the user doesn't have access to the subscription, and they don't pay the renewal price. At the end of the pause period, the subscription resumes and Google attempts to renew the subscription. If the resume is successful, the subscription becomes active again. If the resume fails due to a payment issue, the user enters the account hold state as shown in figures 5 and 6: Figure 5. A user pauses and then resumes their subscription. Figure 6. A user pauses their subscription and then enters account hold. A user can also choose to manually resume a subscription at any time during the pause period, as shown in figure 6. When a user resumes manually, the billing date changes to the manual resume date. When a user's subscription is paused, the Play Billing Library doesn't return the subscription through the queryPurchasesAsync() method. If the subscription is resumed, the queryPurchasesAsync() method returns it again. Listen for RTDNs to be aware of when a user pauses their subscription. These notifications also allow you to notify your users in your app that they have paused their subscription and don't have access to it. You should also provide a way for the user to manually resume their subscription at any time by using a deep link to Google Play . A SubscriptionNotification message with type SUBSCRIPTION_PAUSE_SCHEDULE_CHANGED is sent when your user initiates a pause of their subscription. At this time, the user should keep access to their subscription until the next renewal date, and the subscription resource contains autoRenewEnabled = true . The value of the subscriptionState field is SUBSCRIPTION_STATE_ACTIVE at this point. A SubscriptionNotification message with type SUBSCRIPTION_PAUSED is sent when the pause goes into effect. When this happens, the user should lose access to their subscription, and the subscription resource contains autoRenewEnabled = true , and the subscriptionState field is set to SUBSCRIPTION_STATE_PAUSED . You can see when the subscription is expected to renew again by checking the PausedStateContext object. A SubscriptionNotification message with type SUBSCRIPTION_RENEWED is sent if the subscription is resumed either automatically at the end of the pause period or if the user chose to manually resume the subscription. This should be handled as described in Renewals . A SubscriptionNotification message with type SUBSCRIPTION_ON_HOLD is sent if there was a payment failure while trying to resume the subscription after pause. This should be handled as described in Account hold . Resubscribe For auto-renewing subscription base plans, the Google Play Store may display a Resubscribe button. This button allows users to regain access to a subscription. It may not appear for various reasons, for example when a subscription expired a long time ago. Figure 7. Account > Subscriptions section of the Google Play Store app showing a canceled subscription with a Resubscribe button. Although the button is always labeled Resubscribe , its functionality depends upon the subscription state. While a subscription is canceled but not yet expired, the user is still subscribed and receiving subscription benefits. If the user taps Resubscribe, the cancelation is effectively undone, and the subscription continues to renew. This action is known as restore in Play developer documentation and APIs. After an auto-renewing subscription has expired, you can allow users to purchase the same subscription base plan. This action is known resubscribe in Play developer documentation and APIs. You can configure this option for each base plan in the Play Console or using the API. Restore prior to expiration Note: All developer apps are required to support Restore. If your app relies solely on the queryPurchasesAsync() method to determine whether a user is entitled to a subscription, then your app should automatically handle restorations because the queryPurchasesAsync() method continues to return canceled purchases before their expiration dates. A restored subscription continues to renew as if it were not canceled. If your app synchronizes subscription state with a backend, you should listen for a SubscriptionNotification message with the type SUBSCRIPTION_RESTARTED . After you receive this RTDN, your app can respond to the notification, record that the subscription is now set to renew, and stop displaying restoration messages in your app. The subscription resource looks similar to the following example: { \"kind\" : \"androidpublisher#subscriptionPurchaseV2\" , ... \"subscriptionState\" : \"SUBSCRIPTION_STATE_ACTIVE\" , ... \"lineItems\" : [ { \"productId\" : \"sub_variant_plan01\" , \"expiryTime\" : ne x t _re ne wal_da te ... } ], } Note: A restored subscription uses the same purchase token from when the subscription was canceled. All cancellation fields are cleared from the subscription resource. Resubscribe after expiration If an auto-renewing base plan is configured using the Google Play Console or API to allow Resubscribe, users can re-purchase an expired subscription in the Google Play Store. These are new purchases. Google Play issues a brand new purchase token, and your backend receives an RTDN with type SUBSCRIPTION_PURCHASED . The purchase status for this type of out-of-app purchase does not include a linkedPurchaseToken associated with the original purchase in that case, because the original subscription expired completely. These are new purchases that your backend must process and acknowledge like any other purchase. Upgrades, downgrades, and resubscribe When a user upgrades, downgrades, or signs up after cancellation from your app before the subscription expires , the old subscription is invalidated and a new subscription is created with a new purchase token. In addition, the subscription resource returned from the Google Play Developer API contains a linkedPurchaseToken field that indicates the old purchase from which the user upgraded, downgraded, or resubscribed. You can use the purchase token in that field to look up the old subscription and identify the existing user account so that you can associate the new purchase with the same account. Before offering upgrade, downgrade, or resubscribe options to a user in your app, you must acknowledge the existing subscription. Any plan change or resubscribe is blocked if the existing subscription is still pending acknowledgement. If the user successfully purchases the upgrade, downgrade, or resubscribe, this is a new purchase which you must acknowledge. The recommended way to do this is to use the Google Play Developer API . The subscription resource looks similar to the following example: { \"kind\" : \"androidpublisher#subscriptionPurchaseV2\" , ... \"subscriptionState\" : \"SUBSCRIPTION_STATE_ACTIVE\" , \"linkedPurchaseToken\" : old_purchase_ t oke n , ... \"lineItems\" : [ { \"productId\" : \"sub_variant_plan01\" , \"expiryTime\" : ne x t _re ne wal_da te , \"autoRenewingPlan\" : { \"autoRenewEnabled\" : true } } ], } Price changes See the price change best practices guide to learn about changing auto-renewing subscription prices and notifying users when appropriate. When a price change is added and for any updates to the price change status, you will receive the SUBSCRIPTION_PRICE_CHANGE_UPDATED RTDN. You can query the purchases.subscriptionsv2.get endpoint to get a subscription resource which will contain price change details for each item in the subscription. When price changes are applied to existing subscribers as opt-in , you will received an RTDN if the user takes action to confirm or reject the new price. Handle user confirmation of an opt-in price change When a user accepts your subscription price increase, you receive a SubscriptionNotification message with type SUBSCRIPTION_PRICE_CHANGE_UPDATED . Note: For subscriptions without addons SUBSCRIPTION_PRICE_CHANGE_CONFIRMED notifications will also be sent. However, this notification is deprecated. For more information, see Deprecations . Handle renewals after price change is applied With a price decrease, or when the subscription price increase renews, you will receive a SubscriptionNotification message with type SUBSCRIPTION_RENEWED . Treat this notification like any other renewal . Handle cases where an opt-in price increase is not accepted If a user hasn't accepted your opt-in price increase before they need to renew at the higher price, they are automatically unsubscribed and you receive a SubscriptionNotification message with type SUBSCRIPTION_CANCELED . Handle this event as described in Cancellations . Users can also cancel their subscriptions for an opt-out price increase following the same mechanism. Handle lifecycle for prepaid plans As with auto-renewing subscriptions, you must acknowledge prepaid plans after each new purchase . In the case of prepaid plans, you must fully process both the initial purchase and any top-ups, because the user has to go through the purchase flow every time. Due to the potential for short prepaid plan durations, it's important to acknowledge the purchase as soon as possible. Prepaid plans with a duration of one week or longer must be acknowledged within 3 days. Prepaid plans with a duration shorter than one week must be acknowledged within half of the plan duration. For example, developers have 1.5 days to acknowledge purchase of a three-day prepaid plan. Warning: If a user on a prepaid plan purchases a top-up and you do not acknowledge the purchase within the corresponding timeframe, the top-up purchase is revoked, the remaining subscription is revoked and canceled, and the user is issued a refund. Figure 8. Lifecycle states and transition events for subscription purchases. A SubscriptionNotification message with type SUBSCRIPTION_PURCHASED is sent to your RTDN client whenever a prepaid plan subscription is purchased, including every top-up. Call the purchases.subscriptionsv2.get method to check for the latest prepaid plan subscription state. A new purchase token is issued for top-up purchases, and you receive the previous purchase token in the linkedPurchaseToken field as part of the new subscription purchase state. The purchase token is valid from subscription signup until 60 days after expiration. After this date, the purchase token is no longer valid to use to call the Google Play Developer API. The subscription resource for a prepaid plan purchase looks similar to the following example: { \"kind\" : \"androidpublisher#subscriptionPurchaseV2\" , \"startTime\" : \"2022-04-22T18:39:58.270Z\" , \"regionCode\" : \"US\" , \"subscriptionState\" : \"SUBSCRIPTION_STATE_ACTIVE\" , \"latestOrderId\" : \"GPA.3333-4137-0319-36762\" , \"acknowledgementState\" : \"ACKNOWLEDGEMENT_STATE_ACKNOWLEDGED\" , \"lineItems\" : [ { \"productId\" : \"prepaid_plan01\" , \"expiryTime\" : expiry_da te , \"prepaidPlan\" : { \"allowExtendAfterTime\" : t imes ta mp_a fter _which_ t opups_are_allowed } } ] } You can see when the entitlement ends in the expiryTime field. Top-up purchases increase the entitlement time by accumulating it. That means that if the user tops up before their original entitlement ends, the new time is added on top of their previous expiration date. You might want to display a message in your app informing the user that their prepaid subscriptions can be extended with a top-up. To know when a user will be able to top-up, check the allowExtendAfterTime field in the subscription resource. Prepaid plans don't auto-renew, so they can't be canceled. If a user wants to cancel a prepaid plan, they can let it reach its expiration date. SubscriptionPurchaseV2 fields for prepaid plans New fields have been added to support prepaid plans, which are extended by the user instead of automatically renewing. All fields apply to prepaid plans as they do for auto-renewing subscriptions, with the following exceptions: [New field] lineItems[0].prepaid_plan.allowExtendAfterTime : denotes when a user will be allowed to buy another top-up to extend their prepaid plan, as a user is allowed to have only one unconsumed top-up at a time. [New field] SubscriptionState : specifies the subscription object state. For prepaid plans, this value is always either ACTIVE , PENDING , or CANCELED . lineItems[0].expiryTime : This field is always present for prepaid plans. paused_state_context : This field is never present, as prepaid plans cannot pause. lineItems[0].auto_renewing_plan : Not present for prepaid plans. canceled_state_context : Not present for prepaid plans, as this field applies only to users who actively cancel a subscription. lineItems[0].productId : This field replaces subscriptionId from previous versions. Content and code samples on this page are subject to the licenses described in the Content License . Java and OpenJDK are trademarks or registered trademarks of Oracle and/or its affiliates. Last updated 2025-05-20 UTC. [[[\"Easy to understand\",\"easyToUnderstand\",\"thumb-up\"],[\"Solved my problem\",\"solvedMyProblem\",\"thumb-up\"],[\"Other\",\"otherUp\",\"thumb-up\"]],[[\"Missing the information I need\",\"missingTheInformationINeed\",\"thumb-down\"],[\"Too complicated / too many steps\",\"tooComplicatedTooManySteps\",\"thumb-down\"],[\"Out of date\",\"outOfDate\",\"thumb-down\"],[\"Samples / code issue\",\"samplesCodeIssue\",\"thumb-down\"],[\"Other\",\"otherDown\",\"thumb-down\"]],[\"Last updated 2025-05-20 UTC.\"],[],[]] X Follow @AndroidDev on X YouTube Check out Android Developers on YouTube LinkedIn Connect with the Android Developers community on LinkedIn More Android Android Android for Enterprise Security Source News Blog Podcasts Discover Gaming Machine Learning Health & Fitness Camera & Media Privacy 5G Android Devices Large screens Wear OS ChromeOS devices Android for cars Android TV Releases Android 15 Android 14 Android 13 Android 12 Android 11 Android 10 Pie Documentation and Downloads Android Studio guide Developers guides API reference Download Studio Android NDK Support Report platform bug Report documentation bug Google Play support Join research studies Android Chrome Firebase Google Cloud Platform All products Privacy License Brand guidelines Manage cookies Get news and tips by email Subscribe English Deutsch Español – América Latina Français Indonesia Italiano Polski Português – Brasil Tiếng Việt Türkçe Русский עברית العربيّة فارسی हिंदी বাংলা ภาษาไทย 中文 – 简体 中文 – 繁體 日本語 한국어"}, "https://developer.android.com/google/play/billing/price-changes": {"hash": "e55881f298410d346ac3ab886883a606", "last_checked": "2025-07-18T17:47:01.833268", "content": "Change subscription prices | Google Play's billing system | Android Developers Skip to main content Essentials Build AI experiences Build AI-powered Android apps with Gemini APIs and more. Get started Get started Start by creating your first app. Go deeper with our training courses or explore app development on your own. Hello world Training courses Tutorials Compose for teams Kotlin for Android Monetization with Play ↗️ Extend by device Build apps that give your users seamless experiences from phones to tablets, watches, headsets, and more. Adaptive apps Android XR Wear OS Android for Cars Android TV ChromeOS Build by category Learn to build for your use case by following Google's prescriptive and opinionated guidance. Games Camera & media Social & messaging Health & fitness Productivity Enterprise apps Get the latest Stay in touch with the latest releases throughout the year, join our preview programs, and give us your feedback. Latest updates Experimental updates Android Studio preview Jetpack & Compose libraries Wear OS releases Privacy Sandbox ↗️ Design & Plan Excellent Experiences Build the best experiences for your best users. Learn more UI Design Design a beautiful user interface using Android best practices. Design for Android Mobile Adaptive UI Android XR Widgets Wear OS Android TV Architecture Design robust, testable, and maintainable app logic and services. Introduction Libraries Navigation Modularization Testing Quality Plan for app quality and align with Play store guidelines. Overview Core value User experience Accessibility Technical quality Excellent Experiences Security Safeguard users against threats and ensure a secure Android experience. Overview Privacy Permissions Identity Fraud prevention Develop Gemini in Android Studio Your AI development companion for Android development. Learn more Get Android Studio Core areas Get the samples and docs for the features you need. Samples User interfaces Background work Data and files Connectivity All core areas ⤵️ Tools and workflow Use the IDE to write and build your app, or create your own pipeline. Write and debug code Build projects Test your app Performance Command-line tools Gradle plugin API Device tech Write code for form factors. Connect devices and share data. Adaptive UI Wear OS Android XR Android Health Android for Cars Android TV All devices ⤵️ Libraries Browse API reference documentation with all the details. Android platform Jetpack libraries Compose libraries Google Play services ↗️ Google Play SDK index ↗️ Google Play Community / English Deutsch Español – América Latina Français Indonesia Italiano Polski Português – Brasil Tiếng Việt Türkçe Русский עברית العربيّة فارسی हिंदी বাংলা ภาษาไทย 中文 – 简体 中文 – 繁體 日本語 한국어 Android Studio Sign in Google Play Overview Play Console Play Billing Play Integrity Play Policies Play Services Brand & marketing Other Play guides Essentials More Design & Plan More Develop More Google Play Overview Play Console Play Billing Play Integrity Play Policies Play Services Brand & marketing Other Play guides Community Android Studio Sell digital content in apps Google Play's billing system About Google Play's billing system Setup Additional resources Play Billing Library Migrate to Billing Library 8 Play Billing Library reference Play Billing Library release notes Library version deprecation FAQ App integration Integrate the library Handle BillingResult response codes Server backend integration About server backend integration Purchase lifecycle and RTDNs One-time products About one-time products One-time product purchase lifecycle Multiple purchase options and offers Subscriptions About subscriptions Subscription with add-ons Subscription lifecycle Change subscription prices Manage subscriptions Google Play Developer API API reference Real-time developer notifications reference Play Developer API release notes API deprecations Best practices Manage your product catalog Run offers and promotions Fight fraud and abuse Developer Payload QueryPurchaseHistory Testing and troubleshooting Test your integration Test BillingResult response codes Monetizing outside of Google Play Billing Alternative Billing About alternative billing In-app integration with User Choice In-app integration without User Choice Interim UX guidelines User choice billing pilot Alternative in-app billing systems Alternative billing without user choice (EEA program) External offers About the program In-app integration Backend Integration Build AI experiences Get started Get started Hello world Training courses Tutorials Compose for teams Kotlin for Android Monetization with Play ↗️ Extend by device Adaptive apps Android XR Wear OS Android for Cars Android TV ChromeOS Build by category Games Camera & media Social & messaging Health & fitness Productivity Enterprise apps Get the latest Latest updates Experimental updates Android Studio preview Jetpack & Compose libraries Wear OS releases Privacy Sandbox ↗️ Excellent Experiences Learn more UI Design Design for Android Mobile Adaptive UI Android XR Widgets Wear OS Android TV Architecture Introduction Libraries Navigation Modularization Testing Quality Overview Core value User experience Accessibility Technical quality Excellent Experiences Security Overview Privacy Permissions Identity Fraud prevention Gemini in Android Studio Learn more Get Android Studio Core areas Samples User interfaces Background work Data and files Connectivity All core areas ⤵️ Tools and workflow Write and debug code Build projects Test your app Performance Command-line tools Gradle plugin API Device tech Adaptive UI Wear OS Android XR Android Health Android for Cars Android TV All devices ⤵️ Libraries Android platform Jetpack libraries Compose libraries Google Play services ↗️ Google Play SDK index ↗️ Reminder: By Aug 31, 2025, all new apps and updates to existing apps must use Billing Library version 7 or newer. If you need more time to update your app, you can request an extension until Nov 1, 2025. Learn about Play Billing Library version deprecation . Google Play's billing system Google Play Play Billing Change subscription prices Stay organized with collections Save and categorize content based on your preferences. You can change the prices of your subscription base plans and offers. For example, you might have digital products that need annual price adjustments, or you might change the set of benefits for a product and want to reflect these changes in the price. For more information about changing subscription prices using the Play Console, see the documentation in the Play Console Help Center . To programmatically change the subscription base plan price, use the monetization.subscriptions.patch method. This method receives a Subscription object with the subscription product configuration that is being changed. Set the new price in the RegionalBasePlanConfig object under the correct base plan in the subscription's basePlans collection. This can be very useful if you have a sizable catalog and you need to make updates to all of your products in a short period of time, or if you have a product catalog management system that automatically makes changes to your Google Play subscription products when changes occur. Warning: You should not change the price of a Subscribe with Google subscription. It could be useful to visit your Play Console change log to look up info about any price changes you have made in the past. The information you can find there includes when the prices were updated, who initiated the change, the regions that were updated, and more. This might assist you in cases where you need to review past price changes or review an accidental price change to assess next steps. Price changes for new subscription purchases When you change the price of a base plan or offer, the new price takes effect within a few hours for all new purchases without you having to take any additional action. Price changes for existing subscribers When you change subscription prices, existing subscribers are unaffected by default; they are placed into a legacy price cohort where they continue to pay their original base plan price when they renew. If desired, you can move existing subscribers to the current base plan price. This is action is called ending a legacy price cohort . Changes to an offer's pricing phases cannot be applied to existing subscribers. For installment subscriptions, price changes for a legacy cohort happen at the end of the active commitment period. You cannot change the price currently being paid for a user who is in the middle of paying their installments. End a legacy price cohort You can choose to end a legacy price cohort at any time. This can be done independently for each region. To end a legacy price through the Play Console, refer to the Play Console Help Center . End a legacy price cohort with the Google Play Developer API To programmaticaly end a legacy price cohort, use the monetization.subscriptions.basePlans.migratePrices method. This method migrates subscribers who are receiving a historical subscription price to the current base plan price for the specified regions. The method also triggers price change notifications to be sent to users who are currently receiving a historical price older than the supplied timestamp. When you send this request, you include a list of RegionalPriceMigrationConfig objects in the request body to configure the price cohort migration. For more information on using legacy price cohorts, see the Play Console Help Center . Price decreases When you end a legacy price cohort and the new price is lower than the price that users in the cohort are paying, Google Play notifies the users by email and these subscribers begin paying the lower price the next time they pay for their base plan. License testers also receive email notifications for price decreases. Price increases When ending a legacy price cohort and the new price is higher than the price that users in the cohort are paying, a price increase occurs. While price decreases apply to existing subscribers the next time they pay for their base plan, price increases may or may not require user action. By default, price increases are opt-in changes for existing subscribers. Users must explicitly accept the higher price before it is first charged, or Google Play automatically cancels their subscription. Users are charged the higher price the next time they pay for their base plan following an advance notification period of 37 days. Starting 30 days before this charge, Play notifies existing subscribers through email and push notifications. During the first seven days after the cohort migration is triggered, no users receive notifications from Google Play. This means that you have seven days from when you initiate an opt-in price increase to notify your existing subscribers before Google Play begins notifying them directly. During this period, you can effectively cancel a pending price increase by making another price change back to the original price. After that seven-day period, each user receives automatic notifications from Google Play 30 days before the first renewal with the new price. In some cases when increasing prices for existing subscribers, you have the option to perform price increases with advance notification to users, but without requiring them to take any action. With this option, unless users opt-out by changing subscription plans or canceling their subscription, they'll be charged the new price the next time they pay for their base plan following an advance notification period. This period varies by country and is either 30 days or 60 days. Beginning that number of days before this charge, Play notifies existing subscribers through email and push notification. Opt-out increases are only available in certain locations with limits on the increase amount and frequency, and are subject to certain developer requirements. You can mark a legacy price cohort migration as an opt-out increase if it meets those criteria, as shown in figure 1. Figure 1. Using Play Console to specify a legacy price cohort migration as an opt-out increase. Communicate your price change to the user Important: This notice must be displayed on all device types where the app is available, including mobile, TV platforms, and streaming devices. The sole exception is watch devices, for which the notice is recommended but not required. You should notify existing subscribers whenever you end their legacy price cohort. For opt-out price increases, you should give users advance notification, and you must show users an in-app notice. Unlike opt-in price increases, there is no seven-day waiting period before Play starts notifying users directly. For opt-in price increases, give users advance notification and inform them of the need to accept the price increase. When you initiate an opt-in price increase, you have seven days to notify your existing subscribers before Google Play begins to notify them directly. We recommend that you notify affected users in your app and provide a deep link to the Play Store subscription screen to help them easily review the new price. When users review an opt-in price increase on the Play Store subscription screen, a dialog similar to figure 2 is shown. Figure 2. Example dialog notifying the user of a subscription price change. Handle user response to an opt-in price change After you have notified existing subscribers of a price change and it's an opt-in increase, they may take action before the new price applies to accept or not accept the price increase. If they do, you will receive an RTDN informing you of the outcome. See the purchase lifecycle guidance to learn how to handle these notifications. If the user doesn't act and they reach the first renewal that the opt-in price will apply to, their subscription is automatically canceled and expired on that renewal date. Accidental price increases This section describes the various scenarios for handling an accidental price change. Opt-in increase - If you've accidentally initiated an opt-in price increase, reverse the change immediately by making another price change back to the original price. Change the base plan price back to the original price and navigate to the legacy price points page to initiate a price decrease to the original price. Existing subscribers are not notified about the accidental price change if the price is reverted within seven days. If the price is reverted back to the old price after seven days, the price change would be cancelled for any users that have not paid the new price. Based on renewal dates, some users may have already received the opt-in email notice. Opt-out increase - You can cancel an accidental opt-out increase by reverting the price back to the original price. Change the base plan price back to the original price and navigate to the legacy price points page to initiate a price decrease to the original price. Depending on when the price is reverted if a user has not already paid the higher price, their price increase would be canceled. Based on renewal dates, some users may have already receives the price increase notice emails. Price decreases - You can cancel a price decrease by reverting the subscription's price back to its original value by using the Google Play Console. Change the base plan price back to the original price and navigate to the legacy price points page to initiate a price increase to the original price. Developers can initiate either opt-in or opt-out (if eligible) to cancel the price decrease. If using opt-out, it would be counted toward the frequency. Google Play determines whether the cancellation is effective for a given user's next renewal based on the timing of this reversion relative to their individual renewal date. A price decrease cancellation is valid if the period between reverting the price to its original value and a user's expected renewal time at the new price exceeds the relevant country-specific notification window (30 or 60 days). The user's next subscription renewal occurs at the original, higher price. A price decrease cancellation is invalid if the period between reverting the price to its original value and a user's expected renewal time at the new price is shorter or equal to the relevant country-specific notification window (30 or 60 days). The user will instead go through the price increase process after being charged the lower price at least once upon the next renewal. The user will then get notified about a price increase. Depending on the mode selected during the price migration users would need to accept the price increase for opt-in price increase or would receive the notifications about an opt out increase. Any frequency and amount limitations for opt out increase would apply in this case. Handle overlapping price changes Make sure that you only do one price change at a time. However, if you perform a price change multiple times, impacted users need to agree only to the latest price change. For example, if you've ended a legacy price cohort with an opt-in price increase, changed the price again, and then performed another opt-in price increase, affected users no longer need to respond to the first price increase because only the second price increase now applies. This behaviour applies for legacy price opt-in and opt-out price increases and price decreases. When you start a new price migration for an item that has an older price migration in progress, Google Play handles it as follows: Old price migration is canceled. Google Play stops the old price migration. In the SubscriptionPurchaseV2 API, you'll see the old price change details marked as CANCELED . You'll also receive a SUBSCRIPTION_PRICE_CHANGE_UPDATED RTDN. New Price Migration takes over. Immediately after, Google Play starts the new price migration. This will appear in SubscriptionPurchaseV2 as either OUTSTANDING (for opt-in increases) or CONFIRMED (for opt-out increases or price decreases). You'll receive another SUBSCRIPTION_PRICE_CHANGE_UPDATED RTDN for the item. The user gets the new price. The user will now be moved to the new price migration, and they won't complete the previous price change. And the user receives the standard notification period for the new price. Test price changes Don't change subscription prices for products owned by active subscribers for testing purposes. You can use the Play Billing Lab app and license testers to test subscription price changes without affecting other active subscribers. See the testing guide to learn more about testing price changes. Examples The examples in this section demonstrate how to apply best practices in different price change scenarios. Example 1: Monthly subscription opt-in price increase On March 3, AltoStrat increases the price for AltoStrat Pro, their premium video streaming subscription, by ending a legacy price cohort. They move users in the legacy price cohort of $1 to the current base plan price of $2. The effective date of the price change is April 9 (37 days after March 3). Alice is an existing subscriber whose next renewal is on March 5. The first renewal after the effective date is on May 5, so she renews on March 5 and on April 5 at the old price ($1). When she renews again on May 5, she is charged the new price ($2). Google Play starts notifying Alice of the price change on April 5, which is 30 days before the first renewal date with the new price. Figure 3. Example price change timeline diagram of a monthly subscription with a March 5 renewal date. Bob is an existing subscriber whose next renewal is on March 29. He renews on March 29 at the old price ($1) because the price change has not yet taken effect. When he renews again on April 29, he is charged the new price ($2). He starts receiving price change notifications on March 30, which is 30 days before the first renewal date with the new price. Figure 4. Example price change timeline diagram of a monthly subscription with a March 29 renewal date. Example 2: 3 month subscription opt-in price increase On March 3, FindMyLove ends a legacy price cohort and increases the 3-month fee for FindMyLove Premium from $1 to the base plan price of $2. The effective date of the price change is April 9 (37 days after March 3). Alice is an existing subscriber whose next renewal is on March 5. Alice renews at the old price ($1) because the price change has not yet taken effect. When she renews again on June 5, she is charged the new price ($2). She starts receiving notification of the price change on May 6, which is 30 days before the first renewal date with the new price. Figure 5. Example price change timeline diagram of a 3 month subscription with a March 5 renewal date. Bob is an existing subscriber whose next renewal is on April 11. Bob renews at the new price ($2) because it's after the effective date for the price change. He starts receiving notifications of the price change on March 12, which is 30 days before the first renewal date with the new price. Figure 6. Example price change timeline diagram of a 3 month subscription with an April 11 renewal date. Example 3: Weekly subscription opt-in price increase On March 3, CutePetsNews ends a legacy price cohort triggering a price migration of weekly fee for Weekly Dog Alerts from $1 to $2. The effective date of the price change is April 9. Alice is an existing subscriber whose next weekly renewal is on March 6. She renews on March 6, March 13, March 20, March 27, and April 3 at the old price ($1) because the price change has not yet taken effect. When she renews again on April 10, she is charged the new price ($2). She starts receiving notification of the price change on March 11, which is 30 days before the first renewal date with the new price. Figure 7. Example price change timeline diagram of a weekly subscription with an April 6 renewal date. Example 4: Monthly subscription with multiple opt-in price changes This example demonstrates how multiple price changes are handled. On March 3, AltoStrat triggers a price migration for AltoStrat Pro, their premium video subscription, increasing the price from $1 per month to $2. On March 10, the developer triggers a second price migration, increasing the price to $3 per month. The effective date of the first price change is April 9 (37 days after March 3). The effective date of the second price change is April 16 (37 days after March 10). Alice's next renewal is on March 5. The first renewal after the effective date is on May 5, so she renews on March 5 and on April 5 at the old price ($1). When she renews again on May 5, she is charged the newest price ($3). She only receives notifications about the second price change because the price changes happened within the 7-day freeze period. She starts receiving notification of the price change on April 5, which is 30 days before the first renewal date with the new price. Figure 8. Example price change timeline diagram of a monthly subscription with multiple price changes and a March 5 renewal date. Example 5: Monthly subscription opt-out price change This example shows how opt-out price increases are handled. AltoStrat needs to make their annual price adjustment to account for programming cost increases. On January 2, they change the price of AltoStrat Pro (their premium video streaming subscription) from $1 to $1.30. This price increase meets the criteria for an opt-out price migration. They immediately end the legacy price cohort, specifying an opt-out migration. Users in this cohort are in regions requiring a 30-day minimum opt-out notification period, so the new price is effective on February 1. Alice is an existing subscriber, charged on the 14th of each month. Due to the 30-day minimum notification period, she pays the old price ($1) on January 14. Google Play starts notifying Alice of the price change on January 15, and she starts paying the new price ($1.30) on February 14. Example 6: 12-month installment subscription opt-in price increase This example shows how price increases are handled for installment subscriptions. On March 3, AltoStrat increases the price for AltoStrat Pro, their premium video streaming subscription, by ending a legacy price cohort. They move users in the legacy price cohort of $1 to the current base plan price of $2. The effective date of the price change is April 9 (37 days after March 3). Alice is an existing subscriber who signed up for a 12-month installment plan followed by monthly auto-renewals on June 10 of the previous year. Her first renewal is on June 10 of the current year. Since Alice is in the middle of paying her installments, she continues to pay $1 on March 10, April 10, and May 10. She has her first renewal on June 10, where she is charged the new price ($2) and switches to a monthly auto-renewal cadence. Google Play starts notifying Alice of the price change on May 11, which is 30 days before the first renewal date with the new price. Content and code samples on this page are subject to the licenses described in the Content License . Java and OpenJDK are trademarks or registered trademarks of Oracle and/or its affiliates. Last updated 2025-05-20 UTC. [[[\"Easy to understand\",\"easyToUnderstand\",\"thumb-up\"],[\"Solved my problem\",\"solvedMyProblem\",\"thumb-up\"],[\"Other\",\"otherUp\",\"thumb-up\"]],[[\"Missing the information I need\",\"missingTheInformationINeed\",\"thumb-down\"],[\"Too complicated / too many steps\",\"tooComplicatedTooManySteps\",\"thumb-down\"],[\"Out of date\",\"outOfDate\",\"thumb-down\"],[\"Samples / code issue\",\"samplesCodeIssue\",\"thumb-down\"],[\"Other\",\"otherDown\",\"thumb-down\"]],[\"Last updated 2025-05-20 UTC.\"],[],[]] X Follow @AndroidDev on X YouTube Check out Android Developers on YouTube LinkedIn Connect with the Android Developers community on LinkedIn More Android Android Android for Enterprise Security Source News Blog Podcasts Discover Gaming Machine Learning Health & Fitness Camera & Media Privacy 5G Android Devices Large screens Wear OS ChromeOS devices Android for cars Android TV Releases Android 15 Android 14 Android 13 Android 12 Android 11 Android 10 Pie Documentation and Downloads Android Studio guide Developers guides API reference Download Studio Android NDK Support Report platform bug Report documentation bug Google Play support Join research studies Android Chrome Firebase Google Cloud Platform All products Privacy License Brand guidelines Manage cookies Get news and tips by email Subscribe English Deutsch Español – América Latina Français Indonesia Italiano Polski Português – Brasil Tiếng Việt Türkçe Русский עברית العربيّة فارسی हिंदी বাংলা ภาษาไทย 中文 – 简体 中文 – 繁體 日本語 한국어"}, "https://developer.android.com/google/play/billing/manage-purchases": {"hash": "de255dd3b105edcaea2b24b2488d6cc8", "last_checked": "2025-07-18T17:47:06.856090", "content": "Manage subscriptions and one-time purchases | Google Play's billing system | Android Developers Skip to main content Essentials Build AI experiences Build AI-powered Android apps with Gemini APIs and more. Get started Get started Start by creating your first app. Go deeper with our training courses or explore app development on your own. Hello world Training courses Tutorials Compose for teams Kotlin for Android Monetization with Play ↗️ Extend by device Build apps that give your users seamless experiences from phones to tablets, watches, headsets, and more. Adaptive apps Android XR Wear OS Android for Cars Android TV ChromeOS Build by category Learn to build for your use case by following Google's prescriptive and opinionated guidance. Games Camera & media Social & messaging Health & fitness Productivity Enterprise apps Get the latest Stay in touch with the latest releases throughout the year, join our preview programs, and give us your feedback. Latest updates Experimental updates Android Studio preview Jetpack & Compose libraries Wear OS releases Privacy Sandbox ↗️ Design & Plan Excellent Experiences Build the best experiences for your best users. Learn more UI Design Design a beautiful user interface using Android best practices. Design for Android Mobile Adaptive UI Android XR Widgets Wear OS Android TV Architecture Design robust, testable, and maintainable app logic and services. Introduction Libraries Navigation Modularization Testing Quality Plan for app quality and align with Play store guidelines. Overview Core value User experience Accessibility Technical quality Excellent Experiences Security Safeguard users against threats and ensure a secure Android experience. Overview Privacy Permissions Identity Fraud prevention Develop Gemini in Android Studio Your AI development companion for Android development. Learn more Get Android Studio Core areas Get the samples and docs for the features you need. Samples User interfaces Background work Data and files Connectivity All core areas ⤵️ Tools and workflow Use the IDE to write and build your app, or create your own pipeline. Write and debug code Build projects Test your app Performance Command-line tools Gradle plugin API Device tech Write code for form factors. Connect devices and share data. Adaptive UI Wear OS Android XR Android Health Android for Cars Android TV All devices ⤵️ Libraries Browse API reference documentation with all the details. Android platform Jetpack libraries Compose libraries Google Play services ↗️ Google Play SDK index ↗️ Google Play Community / English Deutsch Español – América Latina Français Indonesia Italiano Polski Português – Brasil Tiếng Việt Türkçe Русский עברית العربيّة فارسی हिंदी বাংলা ภาษาไทย 中文 – 简体 中文 – 繁體 日本語 한국어 Android Studio Sign in Google Play Overview Play Console Play Billing Play Integrity Play Policies Play Services Brand & marketing Other Play guides Essentials More Design & Plan More Develop More Google Play Overview Play Console Play Billing Play Integrity Play Policies Play Services Brand & marketing Other Play guides Community Android Studio Sell digital content in apps Google Play's billing system About Google Play's billing system Setup Additional resources Play Billing Library Migrate to Billing Library 8 Play Billing Library reference Play Billing Library release notes Library version deprecation FAQ App integration Integrate the library Handle BillingResult response codes Server backend integration About server backend integration Purchase lifecycle and RTDNs One-time products About one-time products One-time product purchase lifecycle Multiple purchase options and offers Subscriptions About subscriptions Subscription with add-ons Subscription lifecycle Change subscription prices Manage subscriptions Google Play Developer API API reference Real-time developer notifications reference Play Developer API release notes API deprecations Best practices Manage your product catalog Run offers and promotions Fight fraud and abuse Developer Payload QueryPurchaseHistory Testing and troubleshooting Test your integration Test BillingResult response codes Monetizing outside of Google Play Billing Alternative Billing About alternative billing In-app integration with User Choice In-app integration without User Choice Interim UX guidelines User choice billing pilot Alternative in-app billing systems Alternative billing without user choice (EEA program) External offers About the program In-app integration Backend Integration Build AI experiences Get started Get started Hello world Training courses Tutorials Compose for teams Kotlin for Android Monetization with Play ↗️ Extend by device Adaptive apps Android XR Wear OS Android for Cars Android TV ChromeOS Build by category Games Camera & media Social & messaging Health & fitness Productivity Enterprise apps Get the latest Latest updates Experimental updates Android Studio preview Jetpack & Compose libraries Wear OS releases Privacy Sandbox ↗️ Excellent Experiences Learn more UI Design Design for Android Mobile Adaptive UI Android XR Widgets Wear OS Android TV Architecture Introduction Libraries Navigation Modularization Testing Quality Overview Core value User experience Accessibility Technical quality Excellent Experiences Security Overview Privacy Permissions Identity Fraud prevention Gemini in Android Studio Learn more Get Android Studio Core areas Samples User interfaces Background work Data and files Connectivity All core areas ⤵️ Tools and workflow Write and debug code Build projects Test your app Performance Command-line tools Gradle plugin API Device tech Adaptive UI Wear OS Android XR Android Health Android for Cars Android TV All devices ⤵️ Libraries Android platform Jetpack libraries Compose libraries Google Play services ↗️ Google Play SDK index ↗️ Reminder: By Aug 31, 2025, all new apps and updates to existing apps must use Billing Library version 7 or newer. If you need more time to update your app, you can request an extension until Nov 1, 2025. Learn about Play Billing Library version deprecation . Google Play's billing system Google Play Play Billing Manage subscriptions and one-time purchases Stay organized with collections Save and categorize content based on your preferences. You may need to take management actions on subscriptions or one-time purchases as part of day-to-day business. For example, your customer service may need to issue total or partial refunds for users, or you might need to revoke entitlements in certain cases. You can manage orders from the Play Console , or if you want to manage them from your own system, you can do so by using the Google Play Developer API . Cancel subscriptions Subscription cancellations can be initiated by users or developers. User initiated cancellations Users can cancel a Google Play subscription at any time using the Play Store. If applicable, you must also provide an option for users to cancel their subscriptions in your app and on your website. The easiest way to enable users to cancel voluntarily is by providing deep links in your app to the Play Store , where they can view and manage their subscriptions. Developer initiated cancellations As a developer, you may also need to trigger cancellations from your backend. The purchases.subscriptions.cancel API lets you cancel a subscription purchase. For example, you could use this method to turn down a legacy service. Cancelling a subscription doesn't issue a refund, and the user retains access until the end of their current billing period. This method lets you specify the following types of cancellations in the cancellationType request body parameter: USER_REQUESTED_STOP_RENEWALS : Cancels the subscription as if users have cancelled from the Play Store. Any installment payments will continue for the remainder of the current commitment period. From the Play Store, users may restore the subscription before it expires, or re-subscribe after it expires if enabled for the base plan. DEVELOPER_REQUESTED_STOP_PAYMENTS : Cancels the subscription and prevents any further payments. Users can't restore or re-subscribe to the subscription from the Play Store, however you can enable them to subscribe again within your app. Note: Specifying cancellationType is optional. If you don't specify the parameter in your purchases.subscriptions.cancel API request, by default, the default behavior is DEVELOPER_REQUESTED_STOP_PAYMENTS . Enable users to restore unexpired subscriptions In some scenarios, you may find it useful to allow users to restore unexpired subscriptions from the Play subscription center after you have triggered the cancellation as a developer. For example, you may want to provide a customized in-app cancellation flow. Based on your business logic, you can decide which cancellations triggered from your backend are restorable by users. To indicate that a user can restore the cancellation, issue a POST request to the purchases.subscriptions.cancel API, and set the cancellationType request parameter to the USER_REQUESTED_STOP_RENEWAL value. Example: Purchase token of the subscription 1a2b3c4d5e6f7g8h9i0j Application package name com.your.app Subscription ID your-subscription-product HTTP POST request: https://androidpublisher.googleapis.com/androidpublisher/v3/applications/com.your.app/purchases/subscriptions/your-subscription-product/tokens/1a2b3c4d5e6f7g8h9i0j:cancel Request body: { \"cancellationType\" : \"USER_REQUESTED_STOP_RENEWAL\" } Enable users to resubscribe expired subscriptions To allow the resubscription of an expired subscription, you must enable the Resubscribe option in the subscription's base plan and then cancel the subscription by setting the cancellationType parameter to the USER_REQUESTED_STOP_RENEWAL value. Enable users to resubscribe only in your application If you have set the cancellationType parameter to DEVELOPER_REQUESTED_STOP _PAYMENTS or haven't set the cancellationType parameter, users can't restore their subscription from the Play subscription center. However, users can sign up again for the subscription through your app if required. Taking this action triggers a SUBSCRIPTION_CANCELED Real-time developer notification. Handle these cancellations as described in Cancellations . Note: If you cancel an installment subscription with the purchases.subscriptions.cancel API, setting the cancellationType parameter to USER_REQUESTED_STOP_RENEWAL , cancels only the next renewal of their installment, while the user still needs to finish the commitment. However, setting the parameter value to DEVELOPER_REQUESTED_STOP_PAYMENTS , stops the next payment. Defer billing Use subscriptions.defer to extend the entitlement period for a subscription. During the deferral period, the user remains subscribed to your content though is not charged for the extra time. When you defer billing for a subscription, the status information is updated accordingly and you see it reflected in the expiryTime field in the purchase status information: For active recurring subscriptions, deferred billing extends the next renewal date. For prepaid plans, deferred billing extends the expiration time. Some examples on how you could use deferred billing are: Give users no-cost access as a special offer, such as giving one week free to existing subscribers for filling out a feedback survey. Give customers no-cost access as a customer-care action, for example after an extended outage that might have affected their ability to use your service. Billing can be deferred by as little as one day and up to a year per API call. To defer the end of the entitlement even further, call the API again before the new expiration date arrives. Taking this action triggers a SUBSCRIPTION_DEFERRED Real-time developer notification. See Defer billing for a subscriber in About subscriptions to learn how to handle these events. Example: FitnessGoals streaming service wants to run a promotion to encourage regular exercise in February. They decide to offer an additional one month of service to any subscriber who exercises with FitnessGoals at least 10 times during the month of February. They track the challenge's results, and on March 1st, they call the subscriptions.defer API for every active subscription purchase belonging to users that met the challenge in February. These users get the benefit of an extra full month of regular exercise videos at no cost, and the users tell all their friends how FitnessGoals helps them stay healthy! Issue refunds and revocations There are many situations where you may want to issue a refund for or revoke access to a subscription or one-time purchase. Fully refund an order by order ID With the orders.refund API, you can issue full refunds for any order within three years of purchase. The orders.refund method receives a revoke parameter indicating whether or not access should be revoked in addition to providing the refund. If you issue a revocation with the refund call for subscription purchase, the subscription is immediately terminated and it triggers a SUBSCRIPTION_REVOKED Real Time Developer Notification. Read the subscription lifecycle management guide Revocations section to learn how to handle these events. Example: To celebrate the beginning of the new world cup, the e-sports app Football-Not-Soccer decides to raffle off free virtual jerseys for all users who purchase new team kits in the first 24 hours. Football-Not-Soccer uses the orders.refund API without passing a revoke parameter to refund the jersey purchases to the winners. Note: Access to a subscription can only be revoked for subscriptions with current entitlement. When using the revoke parameter while refunding an order, make sure the order is the latest associated with the subscription. If it is not , the refund will be successful, but the subscription won't be revoked. If your use case requires revoking access to an active subscription, Use the purchases.subscriptionsv2.revoke API with the purchase ID instead of the refund API with a revoke parameter. Revoke and refund a subscription by purchase token For certain use cases you might need to revoke access to a user's subscription and provide a refund. Play Billing offers revocation methods including full refunds and prorated refunds through the subscriptionsv2.revoke API. With this endpoint, you can specify revocationContext to determine how the refund is calculated. Taking this action triggers a SUBSCRIPTION_REVOKED Real Time Developer Notification. Your app should handle these cancellations as described in Revocations . Example: Purchase with purchase token 1a2b3c4d5e6f7g8h9i0j Application with the package name com.your.app Intent of issuing a prorated refund HTTP POST request: https://androidpublisher.googleapis.com/androidpublisher/v3/applications/com.your.app/purchases/subscriptionsv2/tokens/1a2b3c4d5e6f7g8h9i0j:revoke Request body: { \"revocationContext\" : { \"proratedRefund\" : {} } } Full refunds If you need to terminate a subscription and refund the full amount of the current billing period, issue a full refund. Use the purchases.subscriptionsv2.revoke function, and set \"fullRefund\": {} as the refund type. Example: Maria has an auto-renewing 30-day subscription to SuperMovies streaming monthly plan. Maria encountered some technical issues that prevent her from accessing the content. She contacts customer service on day 3 of her billing cycle stating that she never got access to the subscription. Customer service locates Maria's subscription purchase details in their system and triggers a call to purchases.subscriptionsv2.revoke requesting a full refund. Customer service tells Maria she should get 100% of her subscription price refunded and she is not subscribed to the plan anymore. Prorate refunds If you need to terminate a subscription and partially refund the remaining entitlement time, issue a prorated refund. Use the purchases.subscriptionsv2.revoke function, and set \"proratedRefund\": {} as the refund type. Example: Maria has an auto-renewing 30-day subscription to SuperMovies streaming monthly plan. She has happily used the service for some time. Maria contacts customer service on day 15 of her billing cycle stating that she is moving abroad and won't be able to use the service anymore starting the next day. Customer service locates Maria's subscription purchase details in their system and triggers a call to purchases.subscriptionsv2.revoke requesting a prorated refund. Customer service tells Maria she should get about 50% of her subscription price refunded and that access to the service terminated immediately. Note: The purchases.subscriptionsv2.revoke API is a replacement and improvement of the legacy purchases.subscription.revoke . The main difference is that the new method allows partial refunds while the legacy is capable of only full refunds. Be aware that a user refund is based on the value of the latest order. Content and code samples on this page are subject to the licenses described in the Content License . Java and OpenJDK are trademarks or registered trademarks of Oracle and/or its affiliates. Last updated 2025-05-20 UTC. [[[\"Easy to understand\",\"easyToUnderstand\",\"thumb-up\"],[\"Solved my problem\",\"solvedMyProblem\",\"thumb-up\"],[\"Other\",\"otherUp\",\"thumb-up\"]],[[\"Missing the information I need\",\"missingTheInformationINeed\",\"thumb-down\"],[\"Too complicated / too many steps\",\"tooComplicatedTooManySteps\",\"thumb-down\"],[\"Out of date\",\"outOfDate\",\"thumb-down\"],[\"Samples / code issue\",\"samplesCodeIssue\",\"thumb-down\"],[\"Other\",\"otherDown\",\"thumb-down\"]],[\"Last updated 2025-05-20 UTC.\"],[],[]] X Follow @AndroidDev on X YouTube Check out Android Developers on YouTube LinkedIn Connect with the Android Developers community on LinkedIn More Android Android Android for Enterprise Security Source News Blog Podcasts Discover Gaming Machine Learning Health & Fitness Camera & Media Privacy 5G Android Devices Large screens Wear OS ChromeOS devices Android for cars Android TV Releases Android 15 Android 14 Android 13 Android 12 Android 11 Android 10 Pie Documentation and Downloads Android Studio guide Developers guides API reference Download Studio Android NDK Support Report platform bug Report documentation bug Google Play support Join research studies Android Chrome Firebase Google Cloud Platform All products Privacy License Brand guidelines Manage cookies Get news and tips by email Subscribe English Deutsch Español – América Latina Français Indonesia Italiano Polski Português – Brasil Tiếng Việt Türkçe Русский עברית العربيّة فارسی हिंदी বাংলা ภาษาไทย 中文 – 简体 中文 – 繁體 日本語 한국어"}, "https://httpbin.org/html": {"hash": "7ee0036c333fad460f7682d38ffcac5c", "last_checked": "2025-07-18T17:47:08.840142", "content": "<PERSON> - <PERSON>-<PERSON> Availing himself of the mild, summer-cool weather that now reigned in these latitudes, and in preparation for the peculiarly active pursuits shortly to be anticipated, Perth, the begrimed, blistered old blacksmith, had not removed his portable forge to the hold again, after concluding his contributory work for <PERSON><PERSON>'s leg, but still retained it on deck, fast lashed to ringbolts by the foremast; being now almost incessantly invoked by the headsmen, and harpooneers, and bowsmen to do some little job for them; altering, or repairing, or new shaping their various weapons and boat furniture. Often he would be surrounded by an eager circle, all waiting to be served; holding boat-spades, pike-heads, harpoons, and lances, and jealously watching his every sooty movement, as he toiled. Nevertheless, this old man's was a patient hammer wielded by a patient arm. No murmur, no impatience, no petulance did come from him. Silent, slow, and solemn; bowing over still further his chronically broken back, he toiled away, as if toil were life itself, and the heavy beating of his hammer the heavy beating of his heart. And so it was.—Most miserable! A peculiar walk in this old man, a certain slight but painful appearing yawing in his gait, had at an early period of the voyage excited the curiosity of the mariners. And to the importunity of their persisted questionings he had finally given in; and so it came to pass that every one now knew the shameful story of his wretched fate. Belated, and not innocently, one bitter winter's midnight, on the road running between two country towns, the blacksmith half-stupidly felt the deadly numbness stealing over him, and sought refuge in a leaning, dilapidated barn. The issue was, the loss of the extremities of both feet. Out of this revelation, part by part, at last came out the four acts of the gladness, and the one long, and as yet uncatastrophied fifth act of the grief of his life's drama. He was an old man, who, at the age of nearly sixty, had postponedly encountered that thing in sorrow's technicals called ruin. He had been an artisan of famed excellence, and with plenty to do; owned a house and garden; embraced a youthful, daughter-like, loving wife, and three blithe, ruddy children; every Sunday went to a cheerful-looking church, planted in a grove. But one night, under cover of darkness, and further concealed in a most cunning disguisement, a desperate burglar slid into his happy home, and robbed them all of everything. And darker yet to tell, the blacksmith himself did ignorantly conduct this burglar into his family's heart. It was the Bottle Conjuror! Upon the opening of that fatal cork, forth flew the fiend, and shrivelled up his home. Now, for prudent, most wise, and economic reasons, the blacksmith's shop was in the basement of his dwelling, but with a separate entrance to it; so that always had the young and loving healthy wife listened with no unhappy nervousness, but with vigorous pleasure, to the stout ringing of her young-armed old husband's hammer; whose reverberations, muffled by passing through the floors and walls, came up to her, not unsweetly, in her nursery; and so, to stout Labor's iron lullaby, the blacksmith's infants were rocked to slumber. Oh, woe on woe! Oh, Death, why canst thou not sometimes be timely? Hadst thou taken this old blacksmith to thyself ere his full ruin came upon him, then had the young widow had a delicious grief, and her orphans a truly venerable, legendary sire to dream of in their after years; and all of them a care-killing competency."}, "https://example.com": {"hash": "bbf1f65fcba2c8344757353f3285f8ee", "last_checked": "2025-07-18T17:47:09.597117", "content": "Example Domain Example Domain This domain is for use in illustrative examples in documents. You may use this domain in literature without prior coordination or asking for permission. More information..."}, "https://httpbin.org/json": {"hash": "b5d533a4edaba1f9fbd1f77f70f46ffe", "last_checked": "2025-07-18T17:47:14.822579", "content": "{ \"slideshow\": { \"author\": \"Yours Truly\", \"date\": \"date of publication\", \"slides\": [ { \"title\": \"Wake up to WonderWidgets!\", \"type\": \"all\" }, { \"items\": [ \"Why WonderWidgets are great\", \"Who buys WonderWidgets\" ], \"title\": \"Overview\", \"type\": \"all\" } ], \"title\": \"Sample Slide Show\" } }"}}