from fastmcp import FastMCP

mcp = FastMCP("autoWeb",port=9001)

@mcp.tool()
def generate_java_init_android_code(serverId: str, offerId: str, providerOfferId: str, roleId: str = "") -> str:
    """
    生成CTIPayAPI Android原生 初始化的Java代码 直接返回code
    
    参数:
        serverId: 服务器ID
        offerId: 商品ID
        providerOfferId: 提供商商品ID
        roleId: 角色ID(可选)
    
    返回:
        生成的Java代码字符串
    """
    # 确保所有字符串参数被正确转义
    def escape_str(s):
        return s.replace('"', '\\"')
    
    escaped_serverId = escape_str(serverId)
    escaped_roleId = escape_str(roleId)
    escaped_offerId = escape_str(offerId)
    escaped_providerOfferId = escape_str(providerOfferId)
    
    # 构建代码字符串
    code = f"""CTIPayAPI.singleton().setEnv("sandbox");
CTIPayAPI.singleton().setIDCInfo(DC.instance().idcInfo);
CTIBaseRequest request = new CTIGameRequest();
request.serverId = "{escaped_serverId}";
request.roleId = "{escaped_roleId}"; //游戏有roleId才赋值
request.offerId = "{escaped_offerId}";    
request.providerOfferId = "{escaped_providerOfferId}";
request.mpInfo.payChannel = "gwallet";
request.openid = DC.instance().cfg.openId;
CTIPayAPI.singleton().init(activity, request, callBack);"""
    
    return code

@mcp.tool()
def generate_java_product_android_code(productId: str) -> str:
    """
    生成CTIPayAPI Android原生 获取商品信息/拉物品的Java代码 直接返回code
    
    参数:
        productId: 商品ID
    
    返回:
        生成的Java代码字符串
    """
    # 确保所有字符串参数被正确转义
    def escape_str(s):
        return s.replace('"', '\\"')
    
    escaped_productId = escape_str(productId)
    
    # 构建代码字符串
    code = f"""CTIProductRequest ctiProductRequest = new CTIProductRequest();
List<String> unifiedSkuLists = new ArrayList<>();
unifiedSkuLists.add("{escaped_productId}");
ctiProductRequest.unifiedSkuLists = unifiedSkuLists;
ctiProductRequest.paymentMethod = "gwallet";

CTIPayAPI.singleton().getProductInfo(ctiProductRequest, new ICTIProductInfoCallback(){{
    @Override    
    public void onProductInfoResp(String resp) {{
        try {{
            JSONObject json = new JSONObject(resp);
            String ret = json.getString("retCode");
            String retMsg = json.getString("retMsg");
        }} catch (Exception e) {{
            e.printStackTrace();
        }}
    }}
}});"""
    
    return code

@mcp.tool()
def generate_java_pay_android_code(payInfo: str = "") -> str:
    """
    生成CTIPayAPI Android原生 支付的Java代码 不需要集成初始化数据 仅仅需要payInfo 直接返回code
    
    参数:
        payInfo: payInfo(可选)
    
    返回:
        生成的Java代码字符串
    """
    # 确保所有字符串参数被正确转义
    def escape_str(s):
        return s.replace('"', '\\"')
    
    escaped_payInfo = escape_str(payInfo)

    # 构建代码字符串
    code = f"""CTIPayRequest request = new CTIPayRequest();
request.payInfo = {escaped_payInfo};
CTIBaseRequest request = new CTIGameRequest();
CTIPayAPI.singleton().pay(activity,request,callBack);"""
    
    return code

# 示例使用
if __name__ == "__main__":
    # 示例1: 包含所有参数
    code_with_role = generate_java_init_android_code(
        serverId="1",
        roleId="player123",
        offerId="**********",
        providerOfferId="900000958"
    )
    print("带roleId的代码:\n", code_with_role, "\n")
    
    # 示例2: 不包含roleId
    code_without_role = generate_java_init_android_code(
        serverId="1",
        offerId="**********",
        providerOfferId="900000958"
    )
    print("不带roleId的代码:\n", code_without_role)

    # 示例3: 商品信息代码
    product_code = generate_java_product_android_code(productId="com.example.product1")
    print("商品信息代码:\n", product_code)

    # 示例4: 支付代码
    pay_code = generate_java_pay_android_code(payInfo="test")
    print("支付代码:\n", pay_code)
if __name__ == "__main__":
    mcp.run(transport='sse')