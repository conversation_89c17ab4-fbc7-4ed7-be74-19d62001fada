import hashlib
import json
import os
import time
import threading
from datetime import datetime
import difflib
import subprocess
import urllib.request
import urllib.error
from html.parser import HTMLParser
import re
from http.server import HTTPServer, BaseHTTPRequestHandler
import urllib.parse
import ssl
import random

# 监控的网页URL列表
MONITORED_URLS = [
    "https://developer.android.com/google/play/billing/integrate",
    "https://developer.android.com/google/play/billing/subscription-with-addons",
    "https://developer.android.com/google/play/billing/lifecycle/subscriptions",
    "https://developer.android.com/google/play/billing/price-changes",
    "https://developer.android.com/google/play/billing/manage-purchases",

]

# 存储网页内容哈希的文件
HASH_FILE = "webpage_hashes.json"

class HTMLTextExtractor(HTMLParser):
    """简单的HTML文本提取器"""
    def __init__(self):
        super().__init__()
        self.text_content = []
        self.in_script = False
        self.in_style = False

    def handle_starttag(self, tag, attrs):
        if tag.lower() in ['script', 'style']:
            self.in_script = True
            self.in_style = True

    def handle_endtag(self, tag):
        if tag.lower() in ['script', 'style']:
            self.in_script = False
            self.in_style = False

    def handle_data(self, data):
        if not (self.in_script or self.in_style):
            self.text_content.append(data.strip())

    def get_text(self):
        return ' '.join(chunk for chunk in self.text_content if chunk)

def get_random_user_agent():
    """获取随机User-Agent"""
    user_agents = [
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/120.0',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:109.0) Gecko/20100101 Firefox/120.0'
    ]
    return random.choice(user_agents)

def get_webpage_content_selenium(url):
    """使用Selenium获取网页内容（需要安装selenium和webdriver）"""
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        from selenium.webdriver.common.by import By
        from selenium.webdriver.support.ui import WebDriverWait
        from selenium.webdriver.support import expected_conditions as EC

        # 配置Chrome选项
        chrome_options = Options()
        chrome_options.add_argument('--headless')  # 无头模式
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--window-size=1920,1080')
        chrome_options.add_argument(f'--user-agent={get_random_user_agent()}')
        chrome_options.add_argument('--disable-blink-features=AutomationControlled')
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)

        # 创建WebDriver
        driver = webdriver.Chrome(options=chrome_options)
        driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

        # 访问页面
        driver.get(url)

        # 等待页面加载
        WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.TAG_NAME, "body"))
        )

        # 随机等待，模拟人类行为
        time.sleep(random.uniform(2, 5))

        # 获取页面内容
        html_content = driver.page_source
        driver.quit()

        return html_content

    except ImportError:
        print(f"Selenium未安装，无法使用浏览器自动化方法访问 {url}")
        return None
    except Exception as e:
        print(f"Selenium获取失败 {url}: {str(e)}")
        return None

def get_webpage_content_urllib(url):
    """使用urllib获取网页内容，增强反反爬虫能力"""
    try:
        # 随机延迟
        time.sleep(random.uniform(1, 3))

        # 创建SSL上下文，忽略证书验证
        ctx = ssl.create_default_context()
        ctx.check_hostname = False
        ctx.verify_mode = ssl.CERT_NONE

        # 随机User-Agent和更真实的请求头
        headers = {
            'User-Agent': get_random_user_agent(),
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Language': 'en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-User': '?1',
            'Cache-Control': 'max-age=0',
            'DNT': '1',
            'Sec-CH-UA': '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
            'Sec-CH-UA-Mobile': '?0',
            'Sec-CH-UA-Platform': '"macOS"'
        }

        # 创建请求
        req = urllib.request.Request(url, headers=headers)

        # 创建自定义opener处理重定向
        opener = urllib.request.build_opener(urllib.request.HTTPRedirectHandler)

        # 获取网页内容
        response = opener.open(req, timeout=30, context=ctx)

        # 处理编码
        content = response.read()
        if response.info().get('Content-Encoding') == 'gzip':
            import gzip
            content = gzip.decompress(content)

        html_content = content.decode('utf-8', errors='ignore')
        return html_content

    except Exception as e:
        print(f"urllib增强版获取失败 {url}: {str(e)}")
        return None

def get_webpage_content_curl_advanced(url):
    """使用高级curl方法获取网页内容"""
    try:
        # 随机延迟
        time.sleep(random.uniform(1, 4))

        curl_command = [
            'curl',
            '-L',  # 跟随重定向
            '-s',  # 静默模式
            '--max-time', '45',  # 增加超时时间
            '--max-redirs', '10',
            '--compressed',
            '--http2',  # 使用HTTP/2
            '--user-agent', get_random_user_agent(),
            '--header', 'Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
            '--header', 'Accept-Language: en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7',
            '--header', 'Accept-Encoding: gzip, deflate, br',
            '--header', 'Connection: keep-alive',
            '--header', 'Upgrade-Insecure-Requests: 1',
            '--header', 'Sec-Fetch-Dest: document',
            '--header', 'Sec-Fetch-Mode: navigate',
            '--header', 'Sec-Fetch-Site: none',
            '--header', 'Sec-Fetch-User: ?1',
            '--header', 'DNT: 1',
            '--cookie-jar', '/tmp/cookies.txt',  # 保存cookies
            '--cookie', '/tmp/cookies.txt',      # 使用cookies
            url
        ]

        result = subprocess.run(curl_command, capture_output=True, text=True, timeout=50)

        if result.returncode == 0 and result.stdout.strip():
            return result.stdout
        else:
            print(f"高级curl失败: {url}, 返回码: {result.returncode}")
            return None

    except Exception as e:
        print(f"高级curl异常: {url}, 错误: {str(e)}")
        return None

def get_webpage_content(url):
    """获取网页正文内容 - 多种方法尝试"""
    html_content = None
    methods_tried = []

    # 检查是否是Android开发者网站
    is_android_dev = 'developer.android.com' in url

    if is_android_dev:
        print(f"检测到Android开发者网站，使用特殊方法: {url}")

        # 方法1: 尝试Selenium（如果可用）
        try:
            html_content = get_webpage_content_selenium(url)
            if html_content and html_content.strip():
                methods_tried.append("Selenium")
                print(f"Selenium成功获取内容: {url}")
            else:
                methods_tried.append("Selenium(失败)")
        except Exception as e:
            methods_tried.append(f"Selenium(异常: {str(e)[:50]})")

        # 方法2: 高级curl
        if not html_content:
            html_content = get_webpage_content_curl_advanced(url)
            if html_content and html_content.strip():
                methods_tried.append("高级curl")
                print(f"高级curl成功获取内容: {url}")
            else:
                methods_tried.append("高级curl(失败)")

        # 方法3: 增强urllib
        if not html_content:
            html_content = get_webpage_content_urllib(url)
            if html_content and html_content.strip():
                methods_tried.append("增强urllib")
                print(f"增强urllib成功获取内容: {url}")
            else:
                methods_tried.append("增强urllib(失败)")

    else:
        # 对于非Android开发者网站，使用标准方法
        # 方法1: 标准curl
        try:
            curl_command = [
                'curl', '-L', '-s', '--max-time', '30', '--max-redirs', '10',
                '--compressed', '--user-agent', get_random_user_agent(), url
            ]

            result = subprocess.run(curl_command, capture_output=True, text=True, timeout=35)

            if result.returncode == 0 and result.stdout.strip():
                html_content = result.stdout
                methods_tried.append("标准curl")
            else:
                methods_tried.append("标准curl(失败)")

        except Exception as e:
            methods_tried.append(f"标准curl(异常)")

        # 方法2: urllib备用
        if not html_content:
            html_content = get_webpage_content_urllib(url)
            if html_content and html_content.strip():
                methods_tried.append("urllib")
            else:
                methods_tried.append("urllib(失败)")

    if not html_content or not html_content.strip():
        print(f"所有方法都失败了 {url}, 尝试的方法: {', '.join(methods_tried)}")
        return None

    try:
        # 使用自定义HTML解析器提取文本
        extractor = HTMLTextExtractor()
        extractor.feed(html_content)
        text = extractor.get_text()

        # 清理文本
        text = re.sub(r'\s+', ' ', text).strip()

        print(f"成功获取并解析内容 {url}, 使用方法: {', '.join(methods_tried)}, 文本长度: {len(text)}")
        return text

    except Exception as e:
        print(f"解析HTML失败 {url}: {str(e)}")
        return None

def get_content_hash(content):
    """计算内容的MD5哈希值"""
    if content is None:
        return None
    return hashlib.md5(content.encode('utf-8')).hexdigest()

def load_stored_hashes():
    """加载已存储的哈希值"""
    if os.path.exists(HASH_FILE):
        try:
            with open(HASH_FILE, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"加载哈希文件失败: {str(e)}")
    return {}

def save_hashes(hashes):
    """保存哈希值到文件"""
    try:
        with open(HASH_FILE, 'w', encoding='utf-8') as f:
            json.dump(hashes, f, ensure_ascii=False, indent=2)
    except Exception as e:
        print(f"保存哈希文件失败: {str(e)}")

def get_content_diff(old_content, new_content):
    """获取内容差异"""
    if old_content is None or new_content is None:
        return "无法比较内容差异"

    old_lines = old_content.splitlines()
    new_lines = new_content.splitlines()

    diff = list(difflib.unified_diff(
        old_lines,
        new_lines,
        fromfile='旧版本',
        tofile='新版本',
        lineterm='',
        n=3
    ))

    return '\n'.join(diff) if diff else "内容无变化"

def check_webpage_changes():
    """检查所有监控网页的变动"""
    print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 开始检查网页变动...")

    stored_hashes = load_stored_hashes()
    current_hashes = {}
    changes_detected = []

    for url in MONITORED_URLS:
        print(f"检查: {url}")

        # 获取当前内容
        current_content = get_webpage_content(url)
        if current_content is None:
            print(f"跳过 {url} - 无法获取内容")
            continue

        current_hash = get_content_hash(current_content)
        current_hashes[url] = {
            'hash': current_hash,
            'last_checked': datetime.now().isoformat(),
            'content': current_content  # 临时存储用于比较
        }

        # 检查是否有变动
        if url in stored_hashes:
            stored_hash = stored_hashes[url].get('hash')
            if stored_hash != current_hash:
                print(f"检测到变动: {url}")

                # 获取内容差异
                old_content = stored_hashes[url].get('content', '')
                diff = get_content_diff(old_content, current_content)

                changes_detected.append({
                    'url': url,
                    'old_hash': stored_hash,
                    'new_hash': current_hash,
                    'diff': diff,
                    'timestamp': datetime.now().isoformat()
                })
        else:
            print(f"首次监控: {url}")
            changes_detected.append({
                'url': url,
                'old_hash': None,
                'new_hash': current_hash,
                'diff': "首次监控，无历史内容比较",
                'timestamp': datetime.now().isoformat()
            })

    # 保存当前哈希值（不包含content字段）
    hashes_to_save = {}
    for url, data in current_hashes.items():
        hashes_to_save[url] = {
            'hash': data['hash'],
            'last_checked': data['last_checked'],
            'content': data['content']  # 保存内容用于下次比较
        }
    save_hashes(hashes_to_save)

    # 输出变动结果
    if changes_detected:
        print(f"\n发现 {len(changes_detected)} 个页面有变动:")
        for change in changes_detected:
            print(f"\n{'='*80}")
            print(f"URL: {change['url']}")
            print(f"时间: {change['timestamp']}")
            print(f"旧哈希: {change['old_hash']}")
            print(f"新哈希: {change['new_hash']}")
            print(f"变动内容:\n{change['diff']}")
            print(f"{'='*80}")
    else:
        print("未检测到任何变动")

    return changes_detected

def start_monitoring():
    """启动监控服务"""
    print("启动Android开发者文档监控服务...")

    # 立即执行一次检查
    print("执行初始检查...")
    check_webpage_changes()

    # 启动定时任务循环
    def run_scheduler():
        while True:
            now = datetime.now()
            # 检查是否是每天9点
            if now.hour == 9 and now.minute == 0:
                print(f"[{now.strftime('%Y-%m-%d %H:%M:%S')}] 执行定时检查...")
                check_webpage_changes()
                time.sleep(60)  # 等待一分钟避免重复执行
            else:
                time.sleep(30)  # 每30秒检查一次时间

    scheduler_thread = threading.Thread(target=run_scheduler, daemon=True)
    scheduler_thread.start()
    print("监控服务已启动，每天9:00自动检查页面变动")

class WebMonitorHandler(BaseHTTPRequestHandler):
    """HTTP请求处理器"""

    def do_GET(self):
        """处理GET请求"""
        parsed_path = urllib.parse.urlparse(self.path)
        path = parsed_path.path
        query = urllib.parse.parse_qs(parsed_path.query)

        try:
            if path == '/':
                self.send_response(200)
                self.send_header('Content-type', 'text/html; charset=utf-8')
                self.end_headers()

                html = """
                <!DOCTYPE html>
                <html>
                <head>
                    <title>开发者文档监控服务</title>
                    <meta charset="utf-8">
                </head>
                <body>
                    <h1>开发者文档监控服务</h1>
                    <h2>可用接口:</h2>
                    <ul>
                        <li><a href="/status">GET /status</a> - 获取监控状态</li>
                        <li><a href="/check">GET /check</a> - 立即检查页面变动</li>
                        <li><a href="/start">GET /start</a> - 启动监控服务</li>
                        <li><a href="/urls">GET /urls</a> - 获取监控URL列表</li>
                        <li>GET /add_url?url=URL - 添加新的监控URL</li>
                    </ul>
                    <h2>监控的页面:</h2>
                    <ul>
                """

                for url in MONITORED_URLS:
                    html += f'<li><a href="{url}" target="_blank">{url}</a></li>'

                html += """
                    </ul>
                </body>
                </html>
                """

                self.wfile.write(html.encode('utf-8'))

            elif path == '/status':
                result = get_monitoring_status_info()
                self.send_json_response({"status": "success", "data": result})

            elif path == '/check':
                result = check_webpages_now_info()
                self.send_json_response({"status": "success", "data": result})

            elif path == '/start':
                result = start_webpage_monitoring_info()
                self.send_json_response({"status": "success", "data": result})

            elif path == '/urls':
                # 获取监控URL列表
                self.send_json_response({"status": "success", "data": MONITORED_URLS})

            elif path == '/add_url':
                # 添加新的监控URL
                if 'url' in query and query['url']:
                    new_url = query['url'][0]
                    if new_url not in MONITORED_URLS:
                        MONITORED_URLS.append(new_url)
                        self.send_json_response({"status": "success", "message": f"已添加URL: {new_url}", "total_urls": len(MONITORED_URLS)})
                    else:
                        self.send_json_response({"status": "warning", "message": f"URL已存在: {new_url}"})
                else:
                    self.send_json_response({"status": "error", "message": "缺少url参数"})

            else:
                self.send_response(404)
                self.send_header('Content-type', 'application/json; charset=utf-8')
                self.end_headers()
                self.wfile.write(json.dumps({"status": "error", "message": "Not Found"}).encode('utf-8'))

        except Exception as e:
            self.send_response(500)
            self.send_header('Content-type', 'application/json; charset=utf-8')
            self.end_headers()
            self.wfile.write(json.dumps({"status": "error", "message": str(e)}).encode('utf-8'))

    def send_json_response(self, data):
        """发送JSON响应"""
        self.send_response(200)
        self.send_header('Content-type', 'application/json; charset=utf-8')
        self.end_headers()
        self.wfile.write(json.dumps(data, ensure_ascii=False, indent=2).encode('utf-8'))

    def log_message(self, format, *args):
        """自定义日志格式"""
        print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] {format % args}")

def start_webpage_monitoring_info():
    """启动网页监控服务"""
    try:
        start_monitoring()
        return "开发者文档监控服务已启动，每天9:00自动检查页面变动"
    except Exception as e:
        return f"启动监控服务失败: {str(e)}"

def check_webpages_now_info():
    """立即检查网页变动"""
    try:
        changes = check_webpage_changes()

        if not changes:
            return "检查完成，未发现任何页面变动"

        result = f"检查完成，发现 {len(changes)} 个页面有变动:\n\n"

        for i, change in enumerate(changes, 1):
            result += f"{i}. URL: {change['url']}\n"
            result += f"   时间: {change['timestamp']}\n"
            result += f"   状态: {'首次监控' if change['old_hash'] is None else '内容已变动'}\n"

            if change['old_hash'] is not None:
                result += f"   旧哈希: {change['old_hash'][:16]}...\n"
                result += f"   新哈希: {change['new_hash'][:16]}...\n"

            # 限制差异内容长度，避免输出过长
            diff_preview = change['diff'][:500]
            if len(change['diff']) > 500:
                diff_preview += "\n... (内容过长，已截断)"

            result += f"   变动内容预览:\n{diff_preview}\n"
            result += "-" * 60 + "\n"

        return result

    except Exception as e:
        return f"检查页面变动失败: {str(e)}"

def get_monitoring_status_info():
    """获取监控状态"""
    try:
        stored_hashes = load_stored_hashes()

        if not stored_hashes:
            return "监控服务未运行或无历史记录"

        result = f"监控服务状态:\n"
        result += f"监控页面数量: {len(MONITORED_URLS)}\n"
        result += f"已记录页面数量: {len(stored_hashes)}\n\n"

        result += "监控的页面列表:\n"
        for i, url in enumerate(MONITORED_URLS, 1):
            result += f"{i}. {url}\n"
            if url in stored_hashes:
                last_checked = stored_hashes[url].get('last_checked', '未知')
                result += f"   最后检查: {last_checked}\n"
                result += f"   内容哈希: {stored_hashes[url].get('hash', '未知')[:16]}...\n"
            else:
                result += f"   状态: 未检查\n"
            result += "\n"

        return result

    except Exception as e:
        return f"获取监控状态失败: {str(e)}"

if __name__ == "__main__":
    # 启动网页监控服务
    print("正在启动开发者文档监控服务...")
    start_monitoring()

    # 启动HTTP服务器
    server_address = ('', 9001)
    httpd = HTTPServer(server_address, WebMonitorHandler)
    print(f"HTTP服务器运行在端口 9001")
    print(f"访问 http://localhost:9001 查看监控状态")

    try:
        httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n正在关闭服务器...")
        httpd.server_close()