#!/usr/bin/env python3
#
# LaTeX math to Unicode symbols translation dictionaries for
# the content of math alphabet commands (\mathtt, \mathbf, ...).
# Generated with ``write_mathalphabet2unichar.py`` from the data in
# http://milde.users.sourceforge.net/LUCR/Math/
#
# :Copyright: © 2024 Günter Milde.
# :License: Released under the terms of the `2-Clause BSD license`__, in short:
#
#    Copying and distribution of this file, with or without modification,
#    are permitted in any medium without royalty provided the copyright
#    notice and this notice are preserved.
#    This file is offered as-is, without any warranty.
#
# __ https://opensource.org/licenses/BSD-2-Clause

mathbb = {
    '0': '\U0001d7d8',  # 𝟘 MATHEMATICAL DOUBLE-STRUCK DIGIT ZERO
    '1': '\U0001d7d9',  # 𝟙 MATHEMATICAL DOUBLE-STRUCK DIGIT ONE
    '2': '\U0001d7da',  # 𝟚 MATHEMATICAL DOUBLE-STRUCK DIGIT TWO
    '3': '\U0001d7db',  # 𝟛 MATHEMATICAL DOUBLE-STRUCK DIGIT THREE
    '4': '\U0001d7dc',  # 𝟜 MATHEMATICAL DOUBLE-STRUCK DIGIT FOUR
    '5': '\U0001d7dd',  # 𝟝 MATHEMATICAL DOUBLE-STRUCK DIGIT FIVE
    '6': '\U0001d7de',  # 𝟞 MATHEMATICAL DOUBLE-STRUCK DIGIT SIX
    '7': '\U0001d7df',  # 𝟟 MATHEMATICAL DOUBLE-STRUCK DIGIT SEVEN
    '8': '\U0001d7e0',  # 𝟠 MATHEMATICAL DOUBLE-STRUCK DIGIT EIGHT
    '9': '\U0001d7e1',  # 𝟡 MATHEMATICAL DOUBLE-STRUCK DIGIT NINE
    'A': '\U0001d538',  # 𝔸 MATHEMATICAL DOUBLE-STRUCK CAPITAL A
    'B': '\U0001d539',  # 𝔹 MATHEMATICAL DOUBLE-STRUCK CAPITAL B
    'C': '\u2102',  # ℂ DOUBLE-STRUCK CAPITAL C
    'D': '\U0001d53b',  # 𝔻 MATHEMATICAL DOUBLE-STRUCK CAPITAL D
    'E': '\U0001d53c',  # 𝔼 MATHEMATICAL DOUBLE-STRUCK CAPITAL E
    'F': '\U0001d53d',  # 𝔽 MATHEMATICAL DOUBLE-STRUCK CAPITAL F
    'G': '\U0001d53e',  # 𝔾 MATHEMATICAL DOUBLE-STRUCK CAPITAL G
    'H': '\u210d',  # ℍ DOUBLE-STRUCK CAPITAL H
    'I': '\U0001d540',  # 𝕀 MATHEMATICAL DOUBLE-STRUCK CAPITAL I
    'J': '\U0001d541',  # 𝕁 MATHEMATICAL DOUBLE-STRUCK CAPITAL J
    'K': '\U0001d542',  # 𝕂 MATHEMATICAL DOUBLE-STRUCK CAPITAL K
    'L': '\U0001d543',  # 𝕃 MATHEMATICAL DOUBLE-STRUCK CAPITAL L
    'M': '\U0001d544',  # 𝕄 MATHEMATICAL DOUBLE-STRUCK CAPITAL M
    'N': '\u2115',  # ℕ DOUBLE-STRUCK CAPITAL N
    'O': '\U0001d546',  # 𝕆 MATHEMATICAL DOUBLE-STRUCK CAPITAL O
    'P': '\u2119',  # ℙ DOUBLE-STRUCK CAPITAL P
    'Q': '\u211a',  # ℚ DOUBLE-STRUCK CAPITAL Q
    'R': '\u211d',  # ℝ DOUBLE-STRUCK CAPITAL R
    'S': '\U0001d54a',  # 𝕊 MATHEMATICAL DOUBLE-STRUCK CAPITAL S
    'T': '\U0001d54b',  # 𝕋 MATHEMATICAL DOUBLE-STRUCK CAPITAL T
    'U': '\U0001d54c',  # 𝕌 MATHEMATICAL DOUBLE-STRUCK CAPITAL U
    'V': '\U0001d54d',  # 𝕍 MATHEMATICAL DOUBLE-STRUCK CAPITAL V
    'W': '\U0001d54e',  # 𝕎 MATHEMATICAL DOUBLE-STRUCK CAPITAL W
    'X': '\U0001d54f',  # 𝕏 MATHEMATICAL DOUBLE-STRUCK CAPITAL X
    'Y': '\U0001d550',  # 𝕐 MATHEMATICAL DOUBLE-STRUCK CAPITAL Y
    'Z': '\u2124',  # ℤ DOUBLE-STRUCK CAPITAL Z
    'a': '\U0001d552',  # 𝕒 MATHEMATICAL DOUBLE-STRUCK SMALL A
    'b': '\U0001d553',  # 𝕓 MATHEMATICAL DOUBLE-STRUCK SMALL B
    'c': '\U0001d554',  # 𝕔 MATHEMATICAL DOUBLE-STRUCK SMALL C
    'd': '\U0001d555',  # 𝕕 MATHEMATICAL DOUBLE-STRUCK SMALL D
    'e': '\U0001d556',  # 𝕖 MATHEMATICAL DOUBLE-STRUCK SMALL E
    'f': '\U0001d557',  # 𝕗 MATHEMATICAL DOUBLE-STRUCK SMALL F
    'g': '\U0001d558',  # 𝕘 MATHEMATICAL DOUBLE-STRUCK SMALL G
    'h': '\U0001d559',  # 𝕙 MATHEMATICAL DOUBLE-STRUCK SMALL H
    'i': '\U0001d55a',  # 𝕚 MATHEMATICAL DOUBLE-STRUCK SMALL I
    'j': '\U0001d55b',  # 𝕛 MATHEMATICAL DOUBLE-STRUCK SMALL J
    'k': '\U0001d55c',  # 𝕜 MATHEMATICAL DOUBLE-STRUCK SMALL K
    'l': '\U0001d55d',  # 𝕝 MATHEMATICAL DOUBLE-STRUCK SMALL L
    'm': '\U0001d55e',  # 𝕞 MATHEMATICAL DOUBLE-STRUCK SMALL M
    'n': '\U0001d55f',  # 𝕟 MATHEMATICAL DOUBLE-STRUCK SMALL N
    'o': '\U0001d560',  # 𝕠 MATHEMATICAL DOUBLE-STRUCK SMALL O
    'p': '\U0001d561',  # 𝕡 MATHEMATICAL DOUBLE-STRUCK SMALL P
    'q': '\U0001d562',  # 𝕢 MATHEMATICAL DOUBLE-STRUCK SMALL Q
    'r': '\U0001d563',  # 𝕣 MATHEMATICAL DOUBLE-STRUCK SMALL R
    's': '\U0001d564',  # 𝕤 MATHEMATICAL DOUBLE-STRUCK SMALL S
    't': '\U0001d565',  # 𝕥 MATHEMATICAL DOUBLE-STRUCK SMALL T
    'u': '\U0001d566',  # 𝕦 MATHEMATICAL DOUBLE-STRUCK SMALL U
    'v': '\U0001d567',  # 𝕧 MATHEMATICAL DOUBLE-STRUCK SMALL V
    'w': '\U0001d568',  # 𝕨 MATHEMATICAL DOUBLE-STRUCK SMALL W
    'x': '\U0001d569',  # 𝕩 MATHEMATICAL DOUBLE-STRUCK SMALL X
    'y': '\U0001d56a',  # 𝕪 MATHEMATICAL DOUBLE-STRUCK SMALL Y
    'z': '\U0001d56b',  # 𝕫 MATHEMATICAL DOUBLE-STRUCK SMALL Z
    'Γ': '\u213e',  # ℾ DOUBLE-STRUCK CAPITAL GAMMA
    'Π': '\u213f',  # ℿ DOUBLE-STRUCK CAPITAL PI
    'Σ': '\u2140',  # ⅀ DOUBLE-STRUCK N-ARY SUMMATION
    'γ': '\u213d',  # ℽ DOUBLE-STRUCK SMALL GAMMA
    'π': '\u213c',  # ℼ DOUBLE-STRUCK SMALL PI
    }

mathbf = {
    '0': '\U0001d7ce',  # 𝟎 MATHEMATICAL BOLD DIGIT ZERO
    '1': '\U0001d7cf',  # 𝟏 MATHEMATICAL BOLD DIGIT ONE
    '2': '\U0001d7d0',  # 𝟐 MATHEMATICAL BOLD DIGIT TWO
    '3': '\U0001d7d1',  # 𝟑 MATHEMATICAL BOLD DIGIT THREE
    '4': '\U0001d7d2',  # 𝟒 MATHEMATICAL BOLD DIGIT FOUR
    '5': '\U0001d7d3',  # 𝟓 MATHEMATICAL BOLD DIGIT FIVE
    '6': '\U0001d7d4',  # 𝟔 MATHEMATICAL BOLD DIGIT SIX
    '7': '\U0001d7d5',  # 𝟕 MATHEMATICAL BOLD DIGIT SEVEN
    '8': '\U0001d7d6',  # 𝟖 MATHEMATICAL BOLD DIGIT EIGHT
    '9': '\U0001d7d7',  # 𝟗 MATHEMATICAL BOLD DIGIT NINE
    'A': '\U0001d400',  # 𝐀 MATHEMATICAL BOLD CAPITAL A
    'B': '\U0001d401',  # 𝐁 MATHEMATICAL BOLD CAPITAL B
    'C': '\U0001d402',  # 𝐂 MATHEMATICAL BOLD CAPITAL C
    'D': '\U0001d403',  # 𝐃 MATHEMATICAL BOLD CAPITAL D
    'E': '\U0001d404',  # 𝐄 MATHEMATICAL BOLD CAPITAL E
    'F': '\U0001d405',  # 𝐅 MATHEMATICAL BOLD CAPITAL F
    'G': '\U0001d406',  # 𝐆 MATHEMATICAL BOLD CAPITAL G
    'H': '\U0001d407',  # 𝐇 MATHEMATICAL BOLD CAPITAL H
    'I': '\U0001d408',  # 𝐈 MATHEMATICAL BOLD CAPITAL I
    'J': '\U0001d409',  # 𝐉 MATHEMATICAL BOLD CAPITAL J
    'K': '\U0001d40a',  # 𝐊 MATHEMATICAL BOLD CAPITAL K
    'L': '\U0001d40b',  # 𝐋 MATHEMATICAL BOLD CAPITAL L
    'M': '\U0001d40c',  # 𝐌 MATHEMATICAL BOLD CAPITAL M
    'N': '\U0001d40d',  # 𝐍 MATHEMATICAL BOLD CAPITAL N
    'O': '\U0001d40e',  # 𝐎 MATHEMATICAL BOLD CAPITAL O
    'P': '\U0001d40f',  # 𝐏 MATHEMATICAL BOLD CAPITAL P
    'Q': '\U0001d410',  # 𝐐 MATHEMATICAL BOLD CAPITAL Q
    'R': '\U0001d411',  # 𝐑 MATHEMATICAL BOLD CAPITAL R
    'S': '\U0001d412',  # 𝐒 MATHEMATICAL BOLD CAPITAL S
    'T': '\U0001d413',  # 𝐓 MATHEMATICAL BOLD CAPITAL T
    'U': '\U0001d414',  # 𝐔 MATHEMATICAL BOLD CAPITAL U
    'V': '\U0001d415',  # 𝐕 MATHEMATICAL BOLD CAPITAL V
    'W': '\U0001d416',  # 𝐖 MATHEMATICAL BOLD CAPITAL W
    'X': '\U0001d417',  # 𝐗 MATHEMATICAL BOLD CAPITAL X
    'Y': '\U0001d418',  # 𝐘 MATHEMATICAL BOLD CAPITAL Y
    'Z': '\U0001d419',  # 𝐙 MATHEMATICAL BOLD CAPITAL Z
    'a': '\U0001d41a',  # 𝐚 MATHEMATICAL BOLD SMALL A
    'b': '\U0001d41b',  # 𝐛 MATHEMATICAL BOLD SMALL B
    'c': '\U0001d41c',  # 𝐜 MATHEMATICAL BOLD SMALL C
    'd': '\U0001d41d',  # 𝐝 MATHEMATICAL BOLD SMALL D
    'e': '\U0001d41e',  # 𝐞 MATHEMATICAL BOLD SMALL E
    'f': '\U0001d41f',  # 𝐟 MATHEMATICAL BOLD SMALL F
    'g': '\U0001d420',  # 𝐠 MATHEMATICAL BOLD SMALL G
    'h': '\U0001d421',  # 𝐡 MATHEMATICAL BOLD SMALL H
    'i': '\U0001d422',  # 𝐢 MATHEMATICAL BOLD SMALL I
    'j': '\U0001d423',  # 𝐣 MATHEMATICAL BOLD SMALL J
    'k': '\U0001d424',  # 𝐤 MATHEMATICAL BOLD SMALL K
    'l': '\U0001d425',  # 𝐥 MATHEMATICAL BOLD SMALL L
    'm': '\U0001d426',  # 𝐦 MATHEMATICAL BOLD SMALL M
    'n': '\U0001d427',  # 𝐧 MATHEMATICAL BOLD SMALL N
    'o': '\U0001d428',  # 𝐨 MATHEMATICAL BOLD SMALL O
    'p': '\U0001d429',  # 𝐩 MATHEMATICAL BOLD SMALL P
    'q': '\U0001d42a',  # 𝐪 MATHEMATICAL BOLD SMALL Q
    'r': '\U0001d42b',  # 𝐫 MATHEMATICAL BOLD SMALL R
    's': '\U0001d42c',  # 𝐬 MATHEMATICAL BOLD SMALL S
    't': '\U0001d42d',  # 𝐭 MATHEMATICAL BOLD SMALL T
    'u': '\U0001d42e',  # 𝐮 MATHEMATICAL BOLD SMALL U
    'v': '\U0001d42f',  # 𝐯 MATHEMATICAL BOLD SMALL V
    'w': '\U0001d430',  # 𝐰 MATHEMATICAL BOLD SMALL W
    'x': '\U0001d431',  # 𝐱 MATHEMATICAL BOLD SMALL X
    'y': '\U0001d432',  # 𝐲 MATHEMATICAL BOLD SMALL Y
    'z': '\U0001d433',  # 𝐳 MATHEMATICAL BOLD SMALL Z
    'Γ': '\U0001d6aa',  # 𝚪 MATHEMATICAL BOLD CAPITAL GAMMA
    'Δ': '\U0001d6ab',  # 𝚫 MATHEMATICAL BOLD CAPITAL DELTA
    'Θ': '\U0001d6af',  # 𝚯 MATHEMATICAL BOLD CAPITAL THETA
    'Λ': '\U0001d6b2',  # 𝚲 MATHEMATICAL BOLD CAPITAL LAMDA
    'Ξ': '\U0001d6b5',  # 𝚵 MATHEMATICAL BOLD CAPITAL XI
    'Π': '\U0001d6b7',  # 𝚷 MATHEMATICAL BOLD CAPITAL PI
    'Σ': '\U0001d6ba',  # 𝚺 MATHEMATICAL BOLD CAPITAL SIGMA
    'Υ': '\U0001d6bc',  # 𝚼 MATHEMATICAL BOLD CAPITAL UPSILON
    'Φ': '\U0001d6bd',  # 𝚽 MATHEMATICAL BOLD CAPITAL PHI
    'Ψ': '\U0001d6bf',  # 𝚿 MATHEMATICAL BOLD CAPITAL PSI
    'Ω': '\U0001d6c0',  # 𝛀 MATHEMATICAL BOLD CAPITAL OMEGA
    'α': '\U0001d6c2',  # 𝛂 MATHEMATICAL BOLD SMALL ALPHA
    'β': '\U0001d6c3',  # 𝛃 MATHEMATICAL BOLD SMALL BETA
    'γ': '\U0001d6c4',  # 𝛄 MATHEMATICAL BOLD SMALL GAMMA
    'δ': '\U0001d6c5',  # 𝛅 MATHEMATICAL BOLD SMALL DELTA
    'ε': '\U0001d6c6',  # 𝛆 MATHEMATICAL BOLD SMALL EPSILON
    'ζ': '\U0001d6c7',  # 𝛇 MATHEMATICAL BOLD SMALL ZETA
    'η': '\U0001d6c8',  # 𝛈 MATHEMATICAL BOLD SMALL ETA
    'θ': '\U0001d6c9',  # 𝛉 MATHEMATICAL BOLD SMALL THETA
    'ι': '\U0001d6ca',  # 𝛊 MATHEMATICAL BOLD SMALL IOTA
    'κ': '\U0001d6cb',  # 𝛋 MATHEMATICAL BOLD SMALL KAPPA
    'λ': '\U0001d6cc',  # 𝛌 MATHEMATICAL BOLD SMALL LAMDA
    'μ': '\U0001d6cd',  # 𝛍 MATHEMATICAL BOLD SMALL MU
    'ν': '\U0001d6ce',  # 𝛎 MATHEMATICAL BOLD SMALL NU
    'ξ': '\U0001d6cf',  # 𝛏 MATHEMATICAL BOLD SMALL XI
    'π': '\U0001d6d1',  # 𝛑 MATHEMATICAL BOLD SMALL PI
    'ρ': '\U0001d6d2',  # 𝛒 MATHEMATICAL BOLD SMALL RHO
    'ς': '\U0001d6d3',  # 𝛓 MATHEMATICAL BOLD SMALL FINAL SIGMA
    'σ': '\U0001d6d4',  # 𝛔 MATHEMATICAL BOLD SMALL SIGMA
    'τ': '\U0001d6d5',  # 𝛕 MATHEMATICAL BOLD SMALL TAU
    'υ': '\U0001d6d6',  # 𝛖 MATHEMATICAL BOLD SMALL UPSILON
    'φ': '\U0001d6d7',  # 𝛗 MATHEMATICAL BOLD SMALL PHI
    'χ': '\U0001d6d8',  # 𝛘 MATHEMATICAL BOLD SMALL CHI
    'ψ': '\U0001d6d9',  # 𝛙 MATHEMATICAL BOLD SMALL PSI
    'ω': '\U0001d6da',  # 𝛚 MATHEMATICAL BOLD SMALL OMEGA
    'ϑ': '\U0001d6dd',  # 𝛝 MATHEMATICAL BOLD THETA SYMBOL
    'ϕ': '\U0001d6df',  # 𝛟 MATHEMATICAL BOLD PHI SYMBOL
    'ϖ': '\U0001d6e1',  # 𝛡 MATHEMATICAL BOLD PI SYMBOL
    'Ϝ': '\U0001d7ca',  # 𝟊 MATHEMATICAL BOLD CAPITAL DIGAMMA
    'ϝ': '\U0001d7cb',  # 𝟋 MATHEMATICAL BOLD SMALL DIGAMMA
    'ϰ': '\U0001d6de',  # 𝛞 MATHEMATICAL BOLD KAPPA SYMBOL
    'ϱ': '\U0001d6e0',  # 𝛠 MATHEMATICAL BOLD RHO SYMBOL
    'ϵ': '\U0001d6dc',  # 𝛜 MATHEMATICAL BOLD EPSILON SYMBOL
    '∂': '\U0001d6db',  # 𝛛 MATHEMATICAL BOLD PARTIAL DIFFERENTIAL
    '∇': '\U0001d6c1',  # 𝛁 MATHEMATICAL BOLD NABLA
    }

mathbfit = {
    'A': '\U0001d468',  # 𝑨 MATHEMATICAL BOLD ITALIC CAPITAL A
    'B': '\U0001d469',  # 𝑩 MATHEMATICAL BOLD ITALIC CAPITAL B
    'C': '\U0001d46a',  # 𝑪 MATHEMATICAL BOLD ITALIC CAPITAL C
    'D': '\U0001d46b',  # 𝑫 MATHEMATICAL BOLD ITALIC CAPITAL D
    'E': '\U0001d46c',  # 𝑬 MATHEMATICAL BOLD ITALIC CAPITAL E
    'F': '\U0001d46d',  # 𝑭 MATHEMATICAL BOLD ITALIC CAPITAL F
    'G': '\U0001d46e',  # 𝑮 MATHEMATICAL BOLD ITALIC CAPITAL G
    'H': '\U0001d46f',  # 𝑯 MATHEMATICAL BOLD ITALIC CAPITAL H
    'I': '\U0001d470',  # 𝑰 MATHEMATICAL BOLD ITALIC CAPITAL I
    'J': '\U0001d471',  # 𝑱 MATHEMATICAL BOLD ITALIC CAPITAL J
    'K': '\U0001d472',  # 𝑲 MATHEMATICAL BOLD ITALIC CAPITAL K
    'L': '\U0001d473',  # 𝑳 MATHEMATICAL BOLD ITALIC CAPITAL L
    'M': '\U0001d474',  # 𝑴 MATHEMATICAL BOLD ITALIC CAPITAL M
    'N': '\U0001d475',  # 𝑵 MATHEMATICAL BOLD ITALIC CAPITAL N
    'O': '\U0001d476',  # 𝑶 MATHEMATICAL BOLD ITALIC CAPITAL O
    'P': '\U0001d477',  # 𝑷 MATHEMATICAL BOLD ITALIC CAPITAL P
    'Q': '\U0001d478',  # 𝑸 MATHEMATICAL BOLD ITALIC CAPITAL Q
    'R': '\U0001d479',  # 𝑹 MATHEMATICAL BOLD ITALIC CAPITAL R
    'S': '\U0001d47a',  # 𝑺 MATHEMATICAL BOLD ITALIC CAPITAL S
    'T': '\U0001d47b',  # 𝑻 MATHEMATICAL BOLD ITALIC CAPITAL T
    'U': '\U0001d47c',  # 𝑼 MATHEMATICAL BOLD ITALIC CAPITAL U
    'V': '\U0001d47d',  # 𝑽 MATHEMATICAL BOLD ITALIC CAPITAL V
    'W': '\U0001d47e',  # 𝑾 MATHEMATICAL BOLD ITALIC CAPITAL W
    'X': '\U0001d47f',  # 𝑿 MATHEMATICAL BOLD ITALIC CAPITAL X
    'Y': '\U0001d480',  # 𝒀 MATHEMATICAL BOLD ITALIC CAPITAL Y
    'Z': '\U0001d481',  # 𝒁 MATHEMATICAL BOLD ITALIC CAPITAL Z
    'a': '\U0001d482',  # 𝒂 MATHEMATICAL BOLD ITALIC SMALL A
    'b': '\U0001d483',  # 𝒃 MATHEMATICAL BOLD ITALIC SMALL B
    'c': '\U0001d484',  # 𝒄 MATHEMATICAL BOLD ITALIC SMALL C
    'd': '\U0001d485',  # 𝒅 MATHEMATICAL BOLD ITALIC SMALL D
    'e': '\U0001d486',  # 𝒆 MATHEMATICAL BOLD ITALIC SMALL E
    'f': '\U0001d487',  # 𝒇 MATHEMATICAL BOLD ITALIC SMALL F
    'g': '\U0001d488',  # 𝒈 MATHEMATICAL BOLD ITALIC SMALL G
    'h': '\U0001d489',  # 𝒉 MATHEMATICAL BOLD ITALIC SMALL H
    'i': '\U0001d48a',  # 𝒊 MATHEMATICAL BOLD ITALIC SMALL I
    'j': '\U0001d48b',  # 𝒋 MATHEMATICAL BOLD ITALIC SMALL J
    'k': '\U0001d48c',  # 𝒌 MATHEMATICAL BOLD ITALIC SMALL K
    'l': '\U0001d48d',  # 𝒍 MATHEMATICAL BOLD ITALIC SMALL L
    'm': '\U0001d48e',  # 𝒎 MATHEMATICAL BOLD ITALIC SMALL M
    'n': '\U0001d48f',  # 𝒏 MATHEMATICAL BOLD ITALIC SMALL N
    'o': '\U0001d490',  # 𝒐 MATHEMATICAL BOLD ITALIC SMALL O
    'p': '\U0001d491',  # 𝒑 MATHEMATICAL BOLD ITALIC SMALL P
    'q': '\U0001d492',  # 𝒒 MATHEMATICAL BOLD ITALIC SMALL Q
    'r': '\U0001d493',  # 𝒓 MATHEMATICAL BOLD ITALIC SMALL R
    's': '\U0001d494',  # 𝒔 MATHEMATICAL BOLD ITALIC SMALL S
    't': '\U0001d495',  # 𝒕 MATHEMATICAL BOLD ITALIC SMALL T
    'u': '\U0001d496',  # 𝒖 MATHEMATICAL BOLD ITALIC SMALL U
    'v': '\U0001d497',  # 𝒗 MATHEMATICAL BOLD ITALIC SMALL V
    'w': '\U0001d498',  # 𝒘 MATHEMATICAL BOLD ITALIC SMALL W
    'x': '\U0001d499',  # 𝒙 MATHEMATICAL BOLD ITALIC SMALL X
    'y': '\U0001d49a',  # 𝒚 MATHEMATICAL BOLD ITALIC SMALL Y
    'z': '\U0001d49b',  # 𝒛 MATHEMATICAL BOLD ITALIC SMALL Z
    'Γ': '\U0001d71e',  # 𝜞 MATHEMATICAL BOLD ITALIC CAPITAL GAMMA
    'Δ': '\U0001d71f',  # 𝜟 MATHEMATICAL BOLD ITALIC CAPITAL DELTA
    'Θ': '\U0001d723',  # 𝜣 MATHEMATICAL BOLD ITALIC CAPITAL THETA
    'Λ': '\U0001d726',  # 𝜦 MATHEMATICAL BOLD ITALIC CAPITAL LAMDA
    'Ξ': '\U0001d729',  # 𝜩 MATHEMATICAL BOLD ITALIC CAPITAL XI
    'Π': '\U0001d72b',  # 𝜫 MATHEMATICAL BOLD ITALIC CAPITAL PI
    'Σ': '\U0001d72e',  # 𝜮 MATHEMATICAL BOLD ITALIC CAPITAL SIGMA
    'Υ': '\U0001d730',  # 𝜰 MATHEMATICAL BOLD ITALIC CAPITAL UPSILON
    'Φ': '\U0001d731',  # 𝜱 MATHEMATICAL BOLD ITALIC CAPITAL PHI
    'Ψ': '\U0001d733',  # 𝜳 MATHEMATICAL BOLD ITALIC CAPITAL PSI
    'Ω': '\U0001d734',  # 𝜴 MATHEMATICAL BOLD ITALIC CAPITAL OMEGA
    'α': '\U0001d736',  # 𝜶 MATHEMATICAL BOLD ITALIC SMALL ALPHA
    'β': '\U0001d737',  # 𝜷 MATHEMATICAL BOLD ITALIC SMALL BETA
    'γ': '\U0001d738',  # 𝜸 MATHEMATICAL BOLD ITALIC SMALL GAMMA
    'δ': '\U0001d739',  # 𝜹 MATHEMATICAL BOLD ITALIC SMALL DELTA
    'ε': '\U0001d73a',  # 𝜺 MATHEMATICAL BOLD ITALIC SMALL EPSILON
    'ζ': '\U0001d73b',  # 𝜻 MATHEMATICAL BOLD ITALIC SMALL ZETA
    'η': '\U0001d73c',  # 𝜼 MATHEMATICAL BOLD ITALIC SMALL ETA
    'θ': '\U0001d73d',  # 𝜽 MATHEMATICAL BOLD ITALIC SMALL THETA
    'ι': '\U0001d73e',  # 𝜾 MATHEMATICAL BOLD ITALIC SMALL IOTA
    'κ': '\U0001d73f',  # 𝜿 MATHEMATICAL BOLD ITALIC SMALL KAPPA
    'λ': '\U0001d740',  # 𝝀 MATHEMATICAL BOLD ITALIC SMALL LAMDA
    'μ': '\U0001d741',  # 𝝁 MATHEMATICAL BOLD ITALIC SMALL MU
    'ν': '\U0001d742',  # 𝝂 MATHEMATICAL BOLD ITALIC SMALL NU
    'ξ': '\U0001d743',  # 𝝃 MATHEMATICAL BOLD ITALIC SMALL XI
    'π': '\U0001d745',  # 𝝅 MATHEMATICAL BOLD ITALIC SMALL PI
    'ρ': '\U0001d746',  # 𝝆 MATHEMATICAL BOLD ITALIC SMALL RHO
    'ς': '\U0001d747',  # 𝝇 MATHEMATICAL BOLD ITALIC SMALL FINAL SIGMA
    'σ': '\U0001d748',  # 𝝈 MATHEMATICAL BOLD ITALIC SMALL SIGMA
    'τ': '\U0001d749',  # 𝝉 MATHEMATICAL BOLD ITALIC SMALL TAU
    'υ': '\U0001d74a',  # 𝝊 MATHEMATICAL BOLD ITALIC SMALL UPSILON
    'φ': '\U0001d74b',  # 𝝋 MATHEMATICAL BOLD ITALIC SMALL PHI
    'χ': '\U0001d74c',  # 𝝌 MATHEMATICAL BOLD ITALIC SMALL CHI
    'ψ': '\U0001d74d',  # 𝝍 MATHEMATICAL BOLD ITALIC SMALL PSI
    'ω': '\U0001d74e',  # 𝝎 MATHEMATICAL BOLD ITALIC SMALL OMEGA
    'ϑ': '\U0001d751',  # 𝝑 MATHEMATICAL BOLD ITALIC THETA SYMBOL
    'ϕ': '\U0001d753',  # 𝝓 MATHEMATICAL BOLD ITALIC PHI SYMBOL
    'ϖ': '\U0001d755',  # 𝝕 MATHEMATICAL BOLD ITALIC PI SYMBOL
    'ϰ': '\U0001d752',  # 𝝒 MATHEMATICAL BOLD ITALIC KAPPA SYMBOL
    'ϱ': '\U0001d754',  # 𝝔 MATHEMATICAL BOLD ITALIC RHO SYMBOL
    'ϵ': '\U0001d750',  # 𝝐 MATHEMATICAL BOLD ITALIC EPSILON SYMBOL
    '∂': '\U0001d74f',  # 𝝏 MATHEMATICAL BOLD ITALIC PARTIAL DIFFERENTIAL
    '∇': '\U0001d735',  # 𝜵 MATHEMATICAL BOLD ITALIC NABLA
    }

mathcal = {
    'A': '\U0001d49c',  # 𝒜 MATHEMATICAL SCRIPT CAPITAL A
    'B': '\u212c',  # ℬ SCRIPT CAPITAL B
    'C': '\U0001d49e',  # 𝒞 MATHEMATICAL SCRIPT CAPITAL C
    'D': '\U0001d49f',  # 𝒟 MATHEMATICAL SCRIPT CAPITAL D
    'E': '\u2130',  # ℰ SCRIPT CAPITAL E
    'F': '\u2131',  # ℱ SCRIPT CAPITAL F
    'G': '\U0001d4a2',  # 𝒢 MATHEMATICAL SCRIPT CAPITAL G
    'H': '\u210b',  # ℋ SCRIPT CAPITAL H
    'I': '\u2110',  # ℐ SCRIPT CAPITAL I
    'J': '\U0001d4a5',  # 𝒥 MATHEMATICAL SCRIPT CAPITAL J
    'K': '\U0001d4a6',  # 𝒦 MATHEMATICAL SCRIPT CAPITAL K
    'L': '\u2112',  # ℒ SCRIPT CAPITAL L
    'M': '\u2133',  # ℳ SCRIPT CAPITAL M
    'N': '\U0001d4a9',  # 𝒩 MATHEMATICAL SCRIPT CAPITAL N
    'O': '\U0001d4aa',  # 𝒪 MATHEMATICAL SCRIPT CAPITAL O
    'P': '\U0001d4ab',  # 𝒫 MATHEMATICAL SCRIPT CAPITAL P
    'Q': '\U0001d4ac',  # 𝒬 MATHEMATICAL SCRIPT CAPITAL Q
    'R': '\u211b',  # ℛ SCRIPT CAPITAL R
    'S': '\U0001d4ae',  # 𝒮 MATHEMATICAL SCRIPT CAPITAL S
    'T': '\U0001d4af',  # 𝒯 MATHEMATICAL SCRIPT CAPITAL T
    'U': '\U0001d4b0',  # 𝒰 MATHEMATICAL SCRIPT CAPITAL U
    'V': '\U0001d4b1',  # 𝒱 MATHEMATICAL SCRIPT CAPITAL V
    'W': '\U0001d4b2',  # 𝒲 MATHEMATICAL SCRIPT CAPITAL W
    'X': '\U0001d4b3',  # 𝒳 MATHEMATICAL SCRIPT CAPITAL X
    'Y': '\U0001d4b4',  # 𝒴 MATHEMATICAL SCRIPT CAPITAL Y
    'Z': '\U0001d4b5',  # 𝒵 MATHEMATICAL SCRIPT CAPITAL Z
    'a': '\U0001d4b6',  # 𝒶 MATHEMATICAL SCRIPT SMALL A
    'b': '\U0001d4b7',  # 𝒷 MATHEMATICAL SCRIPT SMALL B
    'c': '\U0001d4b8',  # 𝒸 MATHEMATICAL SCRIPT SMALL C
    'd': '\U0001d4b9',  # 𝒹 MATHEMATICAL SCRIPT SMALL D
    'e': '\u212f',  # ℯ SCRIPT SMALL E
    'f': '\U0001d4bb',  # 𝒻 MATHEMATICAL SCRIPT SMALL F
    'g': '\u210a',  # ℊ SCRIPT SMALL G
    'h': '\U0001d4bd',  # 𝒽 MATHEMATICAL SCRIPT SMALL H
    'i': '\U0001d4be',  # 𝒾 MATHEMATICAL SCRIPT SMALL I
    'j': '\U0001d4bf',  # 𝒿 MATHEMATICAL SCRIPT SMALL J
    'k': '\U0001d4c0',  # 𝓀 MATHEMATICAL SCRIPT SMALL K
    'l': '\U0001d4c1',  # 𝓁 MATHEMATICAL SCRIPT SMALL L
    'm': '\U0001d4c2',  # 𝓂 MATHEMATICAL SCRIPT SMALL M
    'n': '\U0001d4c3',  # 𝓃 MATHEMATICAL SCRIPT SMALL N
    'o': '\u2134',  # ℴ SCRIPT SMALL O
    'p': '\U0001d4c5',  # 𝓅 MATHEMATICAL SCRIPT SMALL P
    'q': '\U0001d4c6',  # 𝓆 MATHEMATICAL SCRIPT SMALL Q
    'r': '\U0001d4c7',  # 𝓇 MATHEMATICAL SCRIPT SMALL R
    's': '\U0001d4c8',  # 𝓈 MATHEMATICAL SCRIPT SMALL S
    't': '\U0001d4c9',  # 𝓉 MATHEMATICAL SCRIPT SMALL T
    'u': '\U0001d4ca',  # 𝓊 MATHEMATICAL SCRIPT SMALL U
    'v': '\U0001d4cb',  # 𝓋 MATHEMATICAL SCRIPT SMALL V
    'w': '\U0001d4cc',  # 𝓌 MATHEMATICAL SCRIPT SMALL W
    'x': '\U0001d4cd',  # 𝓍 MATHEMATICAL SCRIPT SMALL X
    'y': '\U0001d4ce',  # 𝓎 MATHEMATICAL SCRIPT SMALL Y
    'z': '\U0001d4cf',  # 𝓏 MATHEMATICAL SCRIPT SMALL Z
    }

mathfrak = {
    'A': '\U0001d504',  # 𝔄 MATHEMATICAL FRAKTUR CAPITAL A
    'B': '\U0001d505',  # 𝔅 MATHEMATICAL FRAKTUR CAPITAL B
    'C': '\u212d',  # ℭ BLACK-LETTER CAPITAL C
    'D': '\U0001d507',  # 𝔇 MATHEMATICAL FRAKTUR CAPITAL D
    'E': '\U0001d508',  # 𝔈 MATHEMATICAL FRAKTUR CAPITAL E
    'F': '\U0001d509',  # 𝔉 MATHEMATICAL FRAKTUR CAPITAL F
    'G': '\U0001d50a',  # 𝔊 MATHEMATICAL FRAKTUR CAPITAL G
    'H': '\u210c',  # ℌ BLACK-LETTER CAPITAL H
    'I': '\u2111',  # ℑ BLACK-LETTER CAPITAL I
    'J': '\U0001d50d',  # 𝔍 MATHEMATICAL FRAKTUR CAPITAL J
    'K': '\U0001d50e',  # 𝔎 MATHEMATICAL FRAKTUR CAPITAL K
    'L': '\U0001d50f',  # 𝔏 MATHEMATICAL FRAKTUR CAPITAL L
    'M': '\U0001d510',  # 𝔐 MATHEMATICAL FRAKTUR CAPITAL M
    'N': '\U0001d511',  # 𝔑 MATHEMATICAL FRAKTUR CAPITAL N
    'O': '\U0001d512',  # 𝔒 MATHEMATICAL FRAKTUR CAPITAL O
    'P': '\U0001d513',  # 𝔓 MATHEMATICAL FRAKTUR CAPITAL P
    'Q': '\U0001d514',  # 𝔔 MATHEMATICAL FRAKTUR CAPITAL Q
    'R': '\u211c',  # ℜ BLACK-LETTER CAPITAL R
    'S': '\U0001d516',  # 𝔖 MATHEMATICAL FRAKTUR CAPITAL S
    'T': '\U0001d517',  # 𝔗 MATHEMATICAL FRAKTUR CAPITAL T
    'U': '\U0001d518',  # 𝔘 MATHEMATICAL FRAKTUR CAPITAL U
    'V': '\U0001d519',  # 𝔙 MATHEMATICAL FRAKTUR CAPITAL V
    'W': '\U0001d51a',  # 𝔚 MATHEMATICAL FRAKTUR CAPITAL W
    'X': '\U0001d51b',  # 𝔛 MATHEMATICAL FRAKTUR CAPITAL X
    'Y': '\U0001d51c',  # 𝔜 MATHEMATICAL FRAKTUR CAPITAL Y
    'Z': '\u2128',  # ℨ BLACK-LETTER CAPITAL Z
    'a': '\U0001d51e',  # 𝔞 MATHEMATICAL FRAKTUR SMALL A
    'b': '\U0001d51f',  # 𝔟 MATHEMATICAL FRAKTUR SMALL B
    'c': '\U0001d520',  # 𝔠 MATHEMATICAL FRAKTUR SMALL C
    'd': '\U0001d521',  # 𝔡 MATHEMATICAL FRAKTUR SMALL D
    'e': '\U0001d522',  # 𝔢 MATHEMATICAL FRAKTUR SMALL E
    'f': '\U0001d523',  # 𝔣 MATHEMATICAL FRAKTUR SMALL F
    'g': '\U0001d524',  # 𝔤 MATHEMATICAL FRAKTUR SMALL G
    'h': '\U0001d525',  # 𝔥 MATHEMATICAL FRAKTUR SMALL H
    'i': '\U0001d526',  # 𝔦 MATHEMATICAL FRAKTUR SMALL I
    'j': '\U0001d527',  # 𝔧 MATHEMATICAL FRAKTUR SMALL J
    'k': '\U0001d528',  # 𝔨 MATHEMATICAL FRAKTUR SMALL K
    'l': '\U0001d529',  # 𝔩 MATHEMATICAL FRAKTUR SMALL L
    'm': '\U0001d52a',  # 𝔪 MATHEMATICAL FRAKTUR SMALL M
    'n': '\U0001d52b',  # 𝔫 MATHEMATICAL FRAKTUR SMALL N
    'o': '\U0001d52c',  # 𝔬 MATHEMATICAL FRAKTUR SMALL O
    'p': '\U0001d52d',  # 𝔭 MATHEMATICAL FRAKTUR SMALL P
    'q': '\U0001d52e',  # 𝔮 MATHEMATICAL FRAKTUR SMALL Q
    'r': '\U0001d52f',  # 𝔯 MATHEMATICAL FRAKTUR SMALL R
    's': '\U0001d530',  # 𝔰 MATHEMATICAL FRAKTUR SMALL S
    't': '\U0001d531',  # 𝔱 MATHEMATICAL FRAKTUR SMALL T
    'u': '\U0001d532',  # 𝔲 MATHEMATICAL FRAKTUR SMALL U
    'v': '\U0001d533',  # 𝔳 MATHEMATICAL FRAKTUR SMALL V
    'w': '\U0001d534',  # 𝔴 MATHEMATICAL FRAKTUR SMALL W
    'x': '\U0001d535',  # 𝔵 MATHEMATICAL FRAKTUR SMALL X
    'y': '\U0001d536',  # 𝔶 MATHEMATICAL FRAKTUR SMALL Y
    'z': '\U0001d537',  # 𝔷 MATHEMATICAL FRAKTUR SMALL Z
    }

mathit = {
    'A': '\U0001d434',  # 𝐴 MATHEMATICAL ITALIC CAPITAL A
    'B': '\U0001d435',  # 𝐵 MATHEMATICAL ITALIC CAPITAL B
    'C': '\U0001d436',  # 𝐶 MATHEMATICAL ITALIC CAPITAL C
    'D': '\U0001d437',  # 𝐷 MATHEMATICAL ITALIC CAPITAL D
    'E': '\U0001d438',  # 𝐸 MATHEMATICAL ITALIC CAPITAL E
    'F': '\U0001d439',  # 𝐹 MATHEMATICAL ITALIC CAPITAL F
    'G': '\U0001d43a',  # 𝐺 MATHEMATICAL ITALIC CAPITAL G
    'H': '\U0001d43b',  # 𝐻 MATHEMATICAL ITALIC CAPITAL H
    'I': '\U0001d43c',  # 𝐼 MATHEMATICAL ITALIC CAPITAL I
    'J': '\U0001d43d',  # 𝐽 MATHEMATICAL ITALIC CAPITAL J
    'K': '\U0001d43e',  # 𝐾 MATHEMATICAL ITALIC CAPITAL K
    'L': '\U0001d43f',  # 𝐿 MATHEMATICAL ITALIC CAPITAL L
    'M': '\U0001d440',  # 𝑀 MATHEMATICAL ITALIC CAPITAL M
    'N': '\U0001d441',  # 𝑁 MATHEMATICAL ITALIC CAPITAL N
    'O': '\U0001d442',  # 𝑂 MATHEMATICAL ITALIC CAPITAL O
    'P': '\U0001d443',  # 𝑃 MATHEMATICAL ITALIC CAPITAL P
    'Q': '\U0001d444',  # 𝑄 MATHEMATICAL ITALIC CAPITAL Q
    'R': '\U0001d445',  # 𝑅 MATHEMATICAL ITALIC CAPITAL R
    'S': '\U0001d446',  # 𝑆 MATHEMATICAL ITALIC CAPITAL S
    'T': '\U0001d447',  # 𝑇 MATHEMATICAL ITALIC CAPITAL T
    'U': '\U0001d448',  # 𝑈 MATHEMATICAL ITALIC CAPITAL U
    'V': '\U0001d449',  # 𝑉 MATHEMATICAL ITALIC CAPITAL V
    'W': '\U0001d44a',  # 𝑊 MATHEMATICAL ITALIC CAPITAL W
    'X': '\U0001d44b',  # 𝑋 MATHEMATICAL ITALIC CAPITAL X
    'Y': '\U0001d44c',  # 𝑌 MATHEMATICAL ITALIC CAPITAL Y
    'Z': '\U0001d44d',  # 𝑍 MATHEMATICAL ITALIC CAPITAL Z
    'a': '\U0001d44e',  # 𝑎 MATHEMATICAL ITALIC SMALL A
    'b': '\U0001d44f',  # 𝑏 MATHEMATICAL ITALIC SMALL B
    'c': '\U0001d450',  # 𝑐 MATHEMATICAL ITALIC SMALL C
    'd': '\U0001d451',  # 𝑑 MATHEMATICAL ITALIC SMALL D
    'e': '\U0001d452',  # 𝑒 MATHEMATICAL ITALIC SMALL E
    'f': '\U0001d453',  # 𝑓 MATHEMATICAL ITALIC SMALL F
    'g': '\U0001d454',  # 𝑔 MATHEMATICAL ITALIC SMALL G
    'h': '\u210e',  # ℎ PLANCK CONSTANT
    'i': '\U0001d456',  # 𝑖 MATHEMATICAL ITALIC SMALL I
    'j': '\U0001d457',  # 𝑗 MATHEMATICAL ITALIC SMALL J
    'k': '\U0001d458',  # 𝑘 MATHEMATICAL ITALIC SMALL K
    'l': '\U0001d459',  # 𝑙 MATHEMATICAL ITALIC SMALL L
    'm': '\U0001d45a',  # 𝑚 MATHEMATICAL ITALIC SMALL M
    'n': '\U0001d45b',  # 𝑛 MATHEMATICAL ITALIC SMALL N
    'o': '\U0001d45c',  # 𝑜 MATHEMATICAL ITALIC SMALL O
    'p': '\U0001d45d',  # 𝑝 MATHEMATICAL ITALIC SMALL P
    'q': '\U0001d45e',  # 𝑞 MATHEMATICAL ITALIC SMALL Q
    'r': '\U0001d45f',  # 𝑟 MATHEMATICAL ITALIC SMALL R
    's': '\U0001d460',  # 𝑠 MATHEMATICAL ITALIC SMALL S
    't': '\U0001d461',  # 𝑡 MATHEMATICAL ITALIC SMALL T
    'u': '\U0001d462',  # 𝑢 MATHEMATICAL ITALIC SMALL U
    'v': '\U0001d463',  # 𝑣 MATHEMATICAL ITALIC SMALL V
    'w': '\U0001d464',  # 𝑤 MATHEMATICAL ITALIC SMALL W
    'x': '\U0001d465',  # 𝑥 MATHEMATICAL ITALIC SMALL X
    'y': '\U0001d466',  # 𝑦 MATHEMATICAL ITALIC SMALL Y
    'z': '\U0001d467',  # 𝑧 MATHEMATICAL ITALIC SMALL Z
    'ı': '\U0001d6a4',  # 𝚤 MATHEMATICAL ITALIC SMALL DOTLESS I
    'ȷ': '\U0001d6a5',  # 𝚥 MATHEMATICAL ITALIC SMALL DOTLESS J
    'Γ': '\U0001d6e4',  # 𝛤 MATHEMATICAL ITALIC CAPITAL GAMMA
    'Δ': '\U0001d6e5',  # 𝛥 MATHEMATICAL ITALIC CAPITAL DELTA
    'Θ': '\U0001d6e9',  # 𝛩 MATHEMATICAL ITALIC CAPITAL THETA
    'Λ': '\U0001d6ec',  # 𝛬 MATHEMATICAL ITALIC CAPITAL LAMDA
    'Ξ': '\U0001d6ef',  # 𝛯 MATHEMATICAL ITALIC CAPITAL XI
    'Π': '\U0001d6f1',  # 𝛱 MATHEMATICAL ITALIC CAPITAL PI
    'Σ': '\U0001d6f4',  # 𝛴 MATHEMATICAL ITALIC CAPITAL SIGMA
    'Υ': '\U0001d6f6',  # 𝛶 MATHEMATICAL ITALIC CAPITAL UPSILON
    'Φ': '\U0001d6f7',  # 𝛷 MATHEMATICAL ITALIC CAPITAL PHI
    'Ψ': '\U0001d6f9',  # 𝛹 MATHEMATICAL ITALIC CAPITAL PSI
    'Ω': '\U0001d6fa',  # 𝛺 MATHEMATICAL ITALIC CAPITAL OMEGA
    'α': '\U0001d6fc',  # 𝛼 MATHEMATICAL ITALIC SMALL ALPHA
    'β': '\U0001d6fd',  # 𝛽 MATHEMATICAL ITALIC SMALL BETA
    'γ': '\U0001d6fe',  # 𝛾 MATHEMATICAL ITALIC SMALL GAMMA
    'δ': '\U0001d6ff',  # 𝛿 MATHEMATICAL ITALIC SMALL DELTA
    'ε': '\U0001d700',  # 𝜀 MATHEMATICAL ITALIC SMALL EPSILON
    'ζ': '\U0001d701',  # 𝜁 MATHEMATICAL ITALIC SMALL ZETA
    'η': '\U0001d702',  # 𝜂 MATHEMATICAL ITALIC SMALL ETA
    'θ': '\U0001d703',  # 𝜃 MATHEMATICAL ITALIC SMALL THETA
    'ι': '\U0001d704',  # 𝜄 MATHEMATICAL ITALIC SMALL IOTA
    'κ': '\U0001d705',  # 𝜅 MATHEMATICAL ITALIC SMALL KAPPA
    'λ': '\U0001d706',  # 𝜆 MATHEMATICAL ITALIC SMALL LAMDA
    'μ': '\U0001d707',  # 𝜇 MATHEMATICAL ITALIC SMALL MU
    'ν': '\U0001d708',  # 𝜈 MATHEMATICAL ITALIC SMALL NU
    'ξ': '\U0001d709',  # 𝜉 MATHEMATICAL ITALIC SMALL XI
    'π': '\U0001d70b',  # 𝜋 MATHEMATICAL ITALIC SMALL PI
    'ρ': '\U0001d70c',  # 𝜌 MATHEMATICAL ITALIC SMALL RHO
    'ς': '\U0001d70d',  # 𝜍 MATHEMATICAL ITALIC SMALL FINAL SIGMA
    'σ': '\U0001d70e',  # 𝜎 MATHEMATICAL ITALIC SMALL SIGMA
    'τ': '\U0001d70f',  # 𝜏 MATHEMATICAL ITALIC SMALL TAU
    'υ': '\U0001d710',  # 𝜐 MATHEMATICAL ITALIC SMALL UPSILON
    'φ': '\U0001d711',  # 𝜑 MATHEMATICAL ITALIC SMALL PHI
    'χ': '\U0001d712',  # 𝜒 MATHEMATICAL ITALIC SMALL CHI
    'ψ': '\U0001d713',  # 𝜓 MATHEMATICAL ITALIC SMALL PSI
    'ω': '\U0001d714',  # 𝜔 MATHEMATICAL ITALIC SMALL OMEGA
    'ϑ': '\U0001d717',  # 𝜗 MATHEMATICAL ITALIC THETA SYMBOL
    'ϕ': '\U0001d719',  # 𝜙 MATHEMATICAL ITALIC PHI SYMBOL
    'ϖ': '\U0001d71b',  # 𝜛 MATHEMATICAL ITALIC PI SYMBOL
    'ϱ': '\U0001d71a',  # 𝜚 MATHEMATICAL ITALIC RHO SYMBOL
    'ϵ': '\U0001d716',  # 𝜖 MATHEMATICAL ITALIC EPSILON SYMBOL
    '∂': '\U0001d715',  # 𝜕 MATHEMATICAL ITALIC PARTIAL DIFFERENTIAL
    '∇': '\U0001d6fb',  # 𝛻 MATHEMATICAL ITALIC NABLA
    }

mathsf = {
    '0': '\U0001d7e2',  # 𝟢 MATHEMATICAL SANS-SERIF DIGIT ZERO
    '1': '\U0001d7e3',  # 𝟣 MATHEMATICAL SANS-SERIF DIGIT ONE
    '2': '\U0001d7e4',  # 𝟤 MATHEMATICAL SANS-SERIF DIGIT TWO
    '3': '\U0001d7e5',  # 𝟥 MATHEMATICAL SANS-SERIF DIGIT THREE
    '4': '\U0001d7e6',  # 𝟦 MATHEMATICAL SANS-SERIF DIGIT FOUR
    '5': '\U0001d7e7',  # 𝟧 MATHEMATICAL SANS-SERIF DIGIT FIVE
    '6': '\U0001d7e8',  # 𝟨 MATHEMATICAL SANS-SERIF DIGIT SIX
    '7': '\U0001d7e9',  # 𝟩 MATHEMATICAL SANS-SERIF DIGIT SEVEN
    '8': '\U0001d7ea',  # 𝟪 MATHEMATICAL SANS-SERIF DIGIT EIGHT
    '9': '\U0001d7eb',  # 𝟫 MATHEMATICAL SANS-SERIF DIGIT NINE
    'A': '\U0001d5a0',  # 𝖠 MATHEMATICAL SANS-SERIF CAPITAL A
    'B': '\U0001d5a1',  # 𝖡 MATHEMATICAL SANS-SERIF CAPITAL B
    'C': '\U0001d5a2',  # 𝖢 MATHEMATICAL SANS-SERIF CAPITAL C
    'D': '\U0001d5a3',  # 𝖣 MATHEMATICAL SANS-SERIF CAPITAL D
    'E': '\U0001d5a4',  # 𝖤 MATHEMATICAL SANS-SERIF CAPITAL E
    'F': '\U0001d5a5',  # 𝖥 MATHEMATICAL SANS-SERIF CAPITAL F
    'G': '\U0001d5a6',  # 𝖦 MATHEMATICAL SANS-SERIF CAPITAL G
    'H': '\U0001d5a7',  # 𝖧 MATHEMATICAL SANS-SERIF CAPITAL H
    'I': '\U0001d5a8',  # 𝖨 MATHEMATICAL SANS-SERIF CAPITAL I
    'J': '\U0001d5a9',  # 𝖩 MATHEMATICAL SANS-SERIF CAPITAL J
    'K': '\U0001d5aa',  # 𝖪 MATHEMATICAL SANS-SERIF CAPITAL K
    'L': '\U0001d5ab',  # 𝖫 MATHEMATICAL SANS-SERIF CAPITAL L
    'M': '\U0001d5ac',  # 𝖬 MATHEMATICAL SANS-SERIF CAPITAL M
    'N': '\U0001d5ad',  # 𝖭 MATHEMATICAL SANS-SERIF CAPITAL N
    'O': '\U0001d5ae',  # 𝖮 MATHEMATICAL SANS-SERIF CAPITAL O
    'P': '\U0001d5af',  # 𝖯 MATHEMATICAL SANS-SERIF CAPITAL P
    'Q': '\U0001d5b0',  # 𝖰 MATHEMATICAL SANS-SERIF CAPITAL Q
    'R': '\U0001d5b1',  # 𝖱 MATHEMATICAL SANS-SERIF CAPITAL R
    'S': '\U0001d5b2',  # 𝖲 MATHEMATICAL SANS-SERIF CAPITAL S
    'T': '\U0001d5b3',  # 𝖳 MATHEMATICAL SANS-SERIF CAPITAL T
    'U': '\U0001d5b4',  # 𝖴 MATHEMATICAL SANS-SERIF CAPITAL U
    'V': '\U0001d5b5',  # 𝖵 MATHEMATICAL SANS-SERIF CAPITAL V
    'W': '\U0001d5b6',  # 𝖶 MATHEMATICAL SANS-SERIF CAPITAL W
    'X': '\U0001d5b7',  # 𝖷 MATHEMATICAL SANS-SERIF CAPITAL X
    'Y': '\U0001d5b8',  # 𝖸 MATHEMATICAL SANS-SERIF CAPITAL Y
    'Z': '\U0001d5b9',  # 𝖹 MATHEMATICAL SANS-SERIF CAPITAL Z
    'a': '\U0001d5ba',  # 𝖺 MATHEMATICAL SANS-SERIF SMALL A
    'b': '\U0001d5bb',  # 𝖻 MATHEMATICAL SANS-SERIF SMALL B
    'c': '\U0001d5bc',  # 𝖼 MATHEMATICAL SANS-SERIF SMALL C
    'd': '\U0001d5bd',  # 𝖽 MATHEMATICAL SANS-SERIF SMALL D
    'e': '\U0001d5be',  # 𝖾 MATHEMATICAL SANS-SERIF SMALL E
    'f': '\U0001d5bf',  # 𝖿 MATHEMATICAL SANS-SERIF SMALL F
    'g': '\U0001d5c0',  # 𝗀 MATHEMATICAL SANS-SERIF SMALL G
    'h': '\U0001d5c1',  # 𝗁 MATHEMATICAL SANS-SERIF SMALL H
    'i': '\U0001d5c2',  # 𝗂 MATHEMATICAL SANS-SERIF SMALL I
    'j': '\U0001d5c3',  # 𝗃 MATHEMATICAL SANS-SERIF SMALL J
    'k': '\U0001d5c4',  # 𝗄 MATHEMATICAL SANS-SERIF SMALL K
    'l': '\U0001d5c5',  # 𝗅 MATHEMATICAL SANS-SERIF SMALL L
    'm': '\U0001d5c6',  # 𝗆 MATHEMATICAL SANS-SERIF SMALL M
    'n': '\U0001d5c7',  # 𝗇 MATHEMATICAL SANS-SERIF SMALL N
    'o': '\U0001d5c8',  # 𝗈 MATHEMATICAL SANS-SERIF SMALL O
    'p': '\U0001d5c9',  # 𝗉 MATHEMATICAL SANS-SERIF SMALL P
    'q': '\U0001d5ca',  # 𝗊 MATHEMATICAL SANS-SERIF SMALL Q
    'r': '\U0001d5cb',  # 𝗋 MATHEMATICAL SANS-SERIF SMALL R
    's': '\U0001d5cc',  # 𝗌 MATHEMATICAL SANS-SERIF SMALL S
    't': '\U0001d5cd',  # 𝗍 MATHEMATICAL SANS-SERIF SMALL T
    'u': '\U0001d5ce',  # 𝗎 MATHEMATICAL SANS-SERIF SMALL U
    'v': '\U0001d5cf',  # 𝗏 MATHEMATICAL SANS-SERIF SMALL V
    'w': '\U0001d5d0',  # 𝗐 MATHEMATICAL SANS-SERIF SMALL W
    'x': '\U0001d5d1',  # 𝗑 MATHEMATICAL SANS-SERIF SMALL X
    'y': '\U0001d5d2',  # 𝗒 MATHEMATICAL SANS-SERIF SMALL Y
    'z': '\U0001d5d3',  # 𝗓 MATHEMATICAL SANS-SERIF SMALL Z
    }

mathsfbf = {
    '0': '\U0001d7ec',  # 𝟬 MATHEMATICAL SANS-SERIF BOLD DIGIT ZERO
    '1': '\U0001d7ed',  # 𝟭 MATHEMATICAL SANS-SERIF BOLD DIGIT ONE
    '2': '\U0001d7ee',  # 𝟮 MATHEMATICAL SANS-SERIF BOLD DIGIT TWO
    '3': '\U0001d7ef',  # 𝟯 MATHEMATICAL SANS-SERIF BOLD DIGIT THREE
    '4': '\U0001d7f0',  # 𝟰 MATHEMATICAL SANS-SERIF BOLD DIGIT FOUR
    '5': '\U0001d7f1',  # 𝟱 MATHEMATICAL SANS-SERIF BOLD DIGIT FIVE
    '6': '\U0001d7f2',  # 𝟲 MATHEMATICAL SANS-SERIF BOLD DIGIT SIX
    '7': '\U0001d7f3',  # 𝟳 MATHEMATICAL SANS-SERIF BOLD DIGIT SEVEN
    '8': '\U0001d7f4',  # 𝟴 MATHEMATICAL SANS-SERIF BOLD DIGIT EIGHT
    '9': '\U0001d7f5',  # 𝟵 MATHEMATICAL SANS-SERIF BOLD DIGIT NINE
    'A': '\U0001d5d4',  # 𝗔 MATHEMATICAL SANS-SERIF BOLD CAPITAL A
    'B': '\U0001d5d5',  # 𝗕 MATHEMATICAL SANS-SERIF BOLD CAPITAL B
    'C': '\U0001d5d6',  # 𝗖 MATHEMATICAL SANS-SERIF BOLD CAPITAL C
    'D': '\U0001d5d7',  # 𝗗 MATHEMATICAL SANS-SERIF BOLD CAPITAL D
    'E': '\U0001d5d8',  # 𝗘 MATHEMATICAL SANS-SERIF BOLD CAPITAL E
    'F': '\U0001d5d9',  # 𝗙 MATHEMATICAL SANS-SERIF BOLD CAPITAL F
    'G': '\U0001d5da',  # 𝗚 MATHEMATICAL SANS-SERIF BOLD CAPITAL G
    'H': '\U0001d5db',  # 𝗛 MATHEMATICAL SANS-SERIF BOLD CAPITAL H
    'I': '\U0001d5dc',  # 𝗜 MATHEMATICAL SANS-SERIF BOLD CAPITAL I
    'J': '\U0001d5dd',  # 𝗝 MATHEMATICAL SANS-SERIF BOLD CAPITAL J
    'K': '\U0001d5de',  # 𝗞 MATHEMATICAL SANS-SERIF BOLD CAPITAL K
    'L': '\U0001d5df',  # 𝗟 MATHEMATICAL SANS-SERIF BOLD CAPITAL L
    'M': '\U0001d5e0',  # 𝗠 MATHEMATICAL SANS-SERIF BOLD CAPITAL M
    'N': '\U0001d5e1',  # 𝗡 MATHEMATICAL SANS-SERIF BOLD CAPITAL N
    'O': '\U0001d5e2',  # 𝗢 MATHEMATICAL SANS-SERIF BOLD CAPITAL O
    'P': '\U0001d5e3',  # 𝗣 MATHEMATICAL SANS-SERIF BOLD CAPITAL P
    'Q': '\U0001d5e4',  # 𝗤 MATHEMATICAL SANS-SERIF BOLD CAPITAL Q
    'R': '\U0001d5e5',  # 𝗥 MATHEMATICAL SANS-SERIF BOLD CAPITAL R
    'S': '\U0001d5e6',  # 𝗦 MATHEMATICAL SANS-SERIF BOLD CAPITAL S
    'T': '\U0001d5e7',  # 𝗧 MATHEMATICAL SANS-SERIF BOLD CAPITAL T
    'U': '\U0001d5e8',  # 𝗨 MATHEMATICAL SANS-SERIF BOLD CAPITAL U
    'V': '\U0001d5e9',  # 𝗩 MATHEMATICAL SANS-SERIF BOLD CAPITAL V
    'W': '\U0001d5ea',  # 𝗪 MATHEMATICAL SANS-SERIF BOLD CAPITAL W
    'X': '\U0001d5eb',  # 𝗫 MATHEMATICAL SANS-SERIF BOLD CAPITAL X
    'Y': '\U0001d5ec',  # 𝗬 MATHEMATICAL SANS-SERIF BOLD CAPITAL Y
    'Z': '\U0001d5ed',  # 𝗭 MATHEMATICAL SANS-SERIF BOLD CAPITAL Z
    'a': '\U0001d5ee',  # 𝗮 MATHEMATICAL SANS-SERIF BOLD SMALL A
    'b': '\U0001d5ef',  # 𝗯 MATHEMATICAL SANS-SERIF BOLD SMALL B
    'c': '\U0001d5f0',  # 𝗰 MATHEMATICAL SANS-SERIF BOLD SMALL C
    'd': '\U0001d5f1',  # 𝗱 MATHEMATICAL SANS-SERIF BOLD SMALL D
    'e': '\U0001d5f2',  # 𝗲 MATHEMATICAL SANS-SERIF BOLD SMALL E
    'f': '\U0001d5f3',  # 𝗳 MATHEMATICAL SANS-SERIF BOLD SMALL F
    'g': '\U0001d5f4',  # 𝗴 MATHEMATICAL SANS-SERIF BOLD SMALL G
    'h': '\U0001d5f5',  # 𝗵 MATHEMATICAL SANS-SERIF BOLD SMALL H
    'i': '\U0001d5f6',  # 𝗶 MATHEMATICAL SANS-SERIF BOLD SMALL I
    'j': '\U0001d5f7',  # 𝗷 MATHEMATICAL SANS-SERIF BOLD SMALL J
    'k': '\U0001d5f8',  # 𝗸 MATHEMATICAL SANS-SERIF BOLD SMALL K
    'l': '\U0001d5f9',  # 𝗹 MATHEMATICAL SANS-SERIF BOLD SMALL L
    'm': '\U0001d5fa',  # 𝗺 MATHEMATICAL SANS-SERIF BOLD SMALL M
    'n': '\U0001d5fb',  # 𝗻 MATHEMATICAL SANS-SERIF BOLD SMALL N
    'o': '\U0001d5fc',  # 𝗼 MATHEMATICAL SANS-SERIF BOLD SMALL O
    'p': '\U0001d5fd',  # 𝗽 MATHEMATICAL SANS-SERIF BOLD SMALL P
    'q': '\U0001d5fe',  # 𝗾 MATHEMATICAL SANS-SERIF BOLD SMALL Q
    'r': '\U0001d5ff',  # 𝗿 MATHEMATICAL SANS-SERIF BOLD SMALL R
    's': '\U0001d600',  # 𝘀 MATHEMATICAL SANS-SERIF BOLD SMALL S
    't': '\U0001d601',  # 𝘁 MATHEMATICAL SANS-SERIF BOLD SMALL T
    'u': '\U0001d602',  # 𝘂 MATHEMATICAL SANS-SERIF BOLD SMALL U
    'v': '\U0001d603',  # 𝘃 MATHEMATICAL SANS-SERIF BOLD SMALL V
    'w': '\U0001d604',  # 𝘄 MATHEMATICAL SANS-SERIF BOLD SMALL W
    'x': '\U0001d605',  # 𝘅 MATHEMATICAL SANS-SERIF BOLD SMALL X
    'y': '\U0001d606',  # 𝘆 MATHEMATICAL SANS-SERIF BOLD SMALL Y
    'z': '\U0001d607',  # 𝘇 MATHEMATICAL SANS-SERIF BOLD SMALL Z
    'Γ': '\U0001d758',  # 𝝘 MATHEMATICAL SANS-SERIF BOLD CAPITAL GAMMA
    'Δ': '\U0001d759',  # 𝝙 MATHEMATICAL SANS-SERIF BOLD CAPITAL DELTA
    'Θ': '\U0001d75d',  # 𝝝 MATHEMATICAL SANS-SERIF BOLD CAPITAL THETA
    'Λ': '\U0001d760',  # 𝝠 MATHEMATICAL SANS-SERIF BOLD CAPITAL LAMDA
    'Ξ': '\U0001d763',  # 𝝣 MATHEMATICAL SANS-SERIF BOLD CAPITAL XI
    'Π': '\U0001d765',  # 𝝥 MATHEMATICAL SANS-SERIF BOLD CAPITAL PI
    'Σ': '\U0001d768',  # 𝝨 MATHEMATICAL SANS-SERIF BOLD CAPITAL SIGMA
    'Υ': '\U0001d76a',  # 𝝪 MATHEMATICAL SANS-SERIF BOLD CAPITAL UPSILON
    'Φ': '\U0001d76b',  # 𝝫 MATHEMATICAL SANS-SERIF BOLD CAPITAL PHI
    'Ψ': '\U0001d76d',  # 𝝭 MATHEMATICAL SANS-SERIF BOLD CAPITAL PSI
    'Ω': '\U0001d76e',  # 𝝮 MATHEMATICAL SANS-SERIF BOLD CAPITAL OMEGA
    'α': '\U0001d770',  # 𝝰 MATHEMATICAL SANS-SERIF BOLD SMALL ALPHA
    'β': '\U0001d771',  # 𝝱 MATHEMATICAL SANS-SERIF BOLD SMALL BETA
    'γ': '\U0001d772',  # 𝝲 MATHEMATICAL SANS-SERIF BOLD SMALL GAMMA
    'δ': '\U0001d773',  # 𝝳 MATHEMATICAL SANS-SERIF BOLD SMALL DELTA
    'ε': '\U0001d774',  # 𝝴 MATHEMATICAL SANS-SERIF BOLD SMALL EPSILON
    'ζ': '\U0001d775',  # 𝝵 MATHEMATICAL SANS-SERIF BOLD SMALL ZETA
    'η': '\U0001d776',  # 𝝶 MATHEMATICAL SANS-SERIF BOLD SMALL ETA
    'θ': '\U0001d777',  # 𝝷 MATHEMATICAL SANS-SERIF BOLD SMALL THETA
    'ι': '\U0001d778',  # 𝝸 MATHEMATICAL SANS-SERIF BOLD SMALL IOTA
    'κ': '\U0001d779',  # 𝝹 MATHEMATICAL SANS-SERIF BOLD SMALL KAPPA
    'λ': '\U0001d77a',  # 𝝺 MATHEMATICAL SANS-SERIF BOLD SMALL LAMDA
    'μ': '\U0001d77b',  # 𝝻 MATHEMATICAL SANS-SERIF BOLD SMALL MU
    'ν': '\U0001d77c',  # 𝝼 MATHEMATICAL SANS-SERIF BOLD SMALL NU
    'ξ': '\U0001d77d',  # 𝝽 MATHEMATICAL SANS-SERIF BOLD SMALL XI
    'π': '\U0001d77f',  # 𝝿 MATHEMATICAL SANS-SERIF BOLD SMALL PI
    'ρ': '\U0001d780',  # 𝞀 MATHEMATICAL SANS-SERIF BOLD SMALL RHO
    'ς': '\U0001d781',  # 𝞁 MATHEMATICAL SANS-SERIF BOLD SMALL FINAL SIGMA
    'σ': '\U0001d782',  # 𝞂 MATHEMATICAL SANS-SERIF BOLD SMALL SIGMA
    'τ': '\U0001d783',  # 𝞃 MATHEMATICAL SANS-SERIF BOLD SMALL TAU
    'υ': '\U0001d784',  # 𝞄 MATHEMATICAL SANS-SERIF BOLD SMALL UPSILON
    'φ': '\U0001d785',  # 𝞅 MATHEMATICAL SANS-SERIF BOLD SMALL PHI
    'χ': '\U0001d786',  # 𝞆 MATHEMATICAL SANS-SERIF BOLD SMALL CHI
    'ψ': '\U0001d787',  # 𝞇 MATHEMATICAL SANS-SERIF BOLD SMALL PSI
    'ω': '\U0001d788',  # 𝞈 MATHEMATICAL SANS-SERIF BOLD SMALL OMEGA
    'ϑ': '\U0001d78b',  # 𝞋 MATHEMATICAL SANS-SERIF BOLD THETA SYMBOL
    'ϕ': '\U0001d78d',  # 𝞍 MATHEMATICAL SANS-SERIF BOLD PHI SYMBOL
    'ϖ': '\U0001d78f',  # 𝞏 MATHEMATICAL SANS-SERIF BOLD PI SYMBOL
    'ϱ': '\U0001d78e',  # 𝞎 MATHEMATICAL SANS-SERIF BOLD RHO SYMBOL
    'ϵ': '\U0001d78a',  # 𝞊 MATHEMATICAL SANS-SERIF BOLD EPSILON SYMBOL
    '∇': '\U0001d76f',  # 𝝯 MATHEMATICAL SANS-SERIF BOLD NABLA
    }

mathsfbfit = {
    'A': '\U0001d63c',  # 𝘼 MATHEMATICAL SANS-SERIF BOLD ITALIC CAPITAL A
    'B': '\U0001d63d',  # 𝘽 MATHEMATICAL SANS-SERIF BOLD ITALIC CAPITAL B
    'C': '\U0001d63e',  # 𝘾 MATHEMATICAL SANS-SERIF BOLD ITALIC CAPITAL C
    'D': '\U0001d63f',  # 𝘿 MATHEMATICAL SANS-SERIF BOLD ITALIC CAPITAL D
    'E': '\U0001d640',  # 𝙀 MATHEMATICAL SANS-SERIF BOLD ITALIC CAPITAL E
    'F': '\U0001d641',  # 𝙁 MATHEMATICAL SANS-SERIF BOLD ITALIC CAPITAL F
    'G': '\U0001d642',  # 𝙂 MATHEMATICAL SANS-SERIF BOLD ITALIC CAPITAL G
    'H': '\U0001d643',  # 𝙃 MATHEMATICAL SANS-SERIF BOLD ITALIC CAPITAL H
    'I': '\U0001d644',  # 𝙄 MATHEMATICAL SANS-SERIF BOLD ITALIC CAPITAL I
    'J': '\U0001d645',  # 𝙅 MATHEMATICAL SANS-SERIF BOLD ITALIC CAPITAL J
    'K': '\U0001d646',  # 𝙆 MATHEMATICAL SANS-SERIF BOLD ITALIC CAPITAL K
    'L': '\U0001d647',  # 𝙇 MATHEMATICAL SANS-SERIF BOLD ITALIC CAPITAL L
    'M': '\U0001d648',  # 𝙈 MATHEMATICAL SANS-SERIF BOLD ITALIC CAPITAL M
    'N': '\U0001d649',  # 𝙉 MATHEMATICAL SANS-SERIF BOLD ITALIC CAPITAL N
    'O': '\U0001d64a',  # 𝙊 MATHEMATICAL SANS-SERIF BOLD ITALIC CAPITAL O
    'P': '\U0001d64b',  # 𝙋 MATHEMATICAL SANS-SERIF BOLD ITALIC CAPITAL P
    'Q': '\U0001d64c',  # 𝙌 MATHEMATICAL SANS-SERIF BOLD ITALIC CAPITAL Q
    'R': '\U0001d64d',  # 𝙍 MATHEMATICAL SANS-SERIF BOLD ITALIC CAPITAL R
    'S': '\U0001d64e',  # 𝙎 MATHEMATICAL SANS-SERIF BOLD ITALIC CAPITAL S
    'T': '\U0001d64f',  # 𝙏 MATHEMATICAL SANS-SERIF BOLD ITALIC CAPITAL T
    'U': '\U0001d650',  # 𝙐 MATHEMATICAL SANS-SERIF BOLD ITALIC CAPITAL U
    'V': '\U0001d651',  # 𝙑 MATHEMATICAL SANS-SERIF BOLD ITALIC CAPITAL V
    'W': '\U0001d652',  # 𝙒 MATHEMATICAL SANS-SERIF BOLD ITALIC CAPITAL W
    'X': '\U0001d653',  # 𝙓 MATHEMATICAL SANS-SERIF BOLD ITALIC CAPITAL X
    'Y': '\U0001d654',  # 𝙔 MATHEMATICAL SANS-SERIF BOLD ITALIC CAPITAL Y
    'Z': '\U0001d655',  # 𝙕 MATHEMATICAL SANS-SERIF BOLD ITALIC CAPITAL Z
    'a': '\U0001d656',  # 𝙖 MATHEMATICAL SANS-SERIF BOLD ITALIC SMALL A
    'b': '\U0001d657',  # 𝙗 MATHEMATICAL SANS-SERIF BOLD ITALIC SMALL B
    'c': '\U0001d658',  # 𝙘 MATHEMATICAL SANS-SERIF BOLD ITALIC SMALL C
    'd': '\U0001d659',  # 𝙙 MATHEMATICAL SANS-SERIF BOLD ITALIC SMALL D
    'e': '\U0001d65a',  # 𝙚 MATHEMATICAL SANS-SERIF BOLD ITALIC SMALL E
    'f': '\U0001d65b',  # 𝙛 MATHEMATICAL SANS-SERIF BOLD ITALIC SMALL F
    'g': '\U0001d65c',  # 𝙜 MATHEMATICAL SANS-SERIF BOLD ITALIC SMALL G
    'h': '\U0001d65d',  # 𝙝 MATHEMATICAL SANS-SERIF BOLD ITALIC SMALL H
    'i': '\U0001d65e',  # 𝙞 MATHEMATICAL SANS-SERIF BOLD ITALIC SMALL I
    'j': '\U0001d65f',  # 𝙟 MATHEMATICAL SANS-SERIF BOLD ITALIC SMALL J
    'k': '\U0001d660',  # 𝙠 MATHEMATICAL SANS-SERIF BOLD ITALIC SMALL K
    'l': '\U0001d661',  # 𝙡 MATHEMATICAL SANS-SERIF BOLD ITALIC SMALL L
    'm': '\U0001d662',  # 𝙢 MATHEMATICAL SANS-SERIF BOLD ITALIC SMALL M
    'n': '\U0001d663',  # 𝙣 MATHEMATICAL SANS-SERIF BOLD ITALIC SMALL N
    'o': '\U0001d664',  # 𝙤 MATHEMATICAL SANS-SERIF BOLD ITALIC SMALL O
    'p': '\U0001d665',  # 𝙥 MATHEMATICAL SANS-SERIF BOLD ITALIC SMALL P
    'q': '\U0001d666',  # 𝙦 MATHEMATICAL SANS-SERIF BOLD ITALIC SMALL Q
    'r': '\U0001d667',  # 𝙧 MATHEMATICAL SANS-SERIF BOLD ITALIC SMALL R
    's': '\U0001d668',  # 𝙨 MATHEMATICAL SANS-SERIF BOLD ITALIC SMALL S
    't': '\U0001d669',  # 𝙩 MATHEMATICAL SANS-SERIF BOLD ITALIC SMALL T
    'u': '\U0001d66a',  # 𝙪 MATHEMATICAL SANS-SERIF BOLD ITALIC SMALL U
    'v': '\U0001d66b',  # 𝙫 MATHEMATICAL SANS-SERIF BOLD ITALIC SMALL V
    'w': '\U0001d66c',  # 𝙬 MATHEMATICAL SANS-SERIF BOLD ITALIC SMALL W
    'x': '\U0001d66d',  # 𝙭 MATHEMATICAL SANS-SERIF BOLD ITALIC SMALL X
    'y': '\U0001d66e',  # 𝙮 MATHEMATICAL SANS-SERIF BOLD ITALIC SMALL Y
    'z': '\U0001d66f',  # 𝙯 MATHEMATICAL SANS-SERIF BOLD ITALIC SMALL Z
    'Γ': '\U0001d792',  # 𝞒 MATHEMATICAL SANS-SERIF BOLD ITALIC CAPITAL GAMMA
    'Δ': '\U0001d793',  # 𝞓 MATHEMATICAL SANS-SERIF BOLD ITALIC CAPITAL DELTA
    'Θ': '\U0001d797',  # 𝞗 MATHEMATICAL SANS-SERIF BOLD ITALIC CAPITAL THETA
    'Λ': '\U0001d79a',  # 𝞚 MATHEMATICAL SANS-SERIF BOLD ITALIC CAPITAL LAMDA
    'Ξ': '\U0001d79d',  # 𝞝 MATHEMATICAL SANS-SERIF BOLD ITALIC CAPITAL XI
    'Π': '\U0001d79f',  # 𝞟 MATHEMATICAL SANS-SERIF BOLD ITALIC CAPITAL PI
    'Σ': '\U0001d7a2',  # 𝞢 MATHEMATICAL SANS-SERIF BOLD ITALIC CAPITAL SIGMA
    'Υ': '\U0001d7a4',  # 𝞤 MATHEMATICAL SANS-SERIF BOLD ITALIC CAPITAL UPSILON
    'Φ': '\U0001d7a5',  # 𝞥 MATHEMATICAL SANS-SERIF BOLD ITALIC CAPITAL PHI
    'Ψ': '\U0001d7a7',  # 𝞧 MATHEMATICAL SANS-SERIF BOLD ITALIC CAPITAL PSI
    'Ω': '\U0001d7a8',  # 𝞨 MATHEMATICAL SANS-SERIF BOLD ITALIC CAPITAL OMEGA
    'α': '\U0001d7aa',  # 𝞪 MATHEMATICAL SANS-SERIF BOLD ITALIC SMALL ALPHA
    'β': '\U0001d7ab',  # 𝞫 MATHEMATICAL SANS-SERIF BOLD ITALIC SMALL BETA
    'γ': '\U0001d7ac',  # 𝞬 MATHEMATICAL SANS-SERIF BOLD ITALIC SMALL GAMMA
    'δ': '\U0001d7ad',  # 𝞭 MATHEMATICAL SANS-SERIF BOLD ITALIC SMALL DELTA
    'ε': '\U0001d7ae',  # 𝞮 MATHEMATICAL SANS-SERIF BOLD ITALIC SMALL EPSILON
    'ζ': '\U0001d7af',  # 𝞯 MATHEMATICAL SANS-SERIF BOLD ITALIC SMALL ZETA
    'η': '\U0001d7b0',  # 𝞰 MATHEMATICAL SANS-SERIF BOLD ITALIC SMALL ETA
    'θ': '\U0001d7b1',  # 𝞱 MATHEMATICAL SANS-SERIF BOLD ITALIC SMALL THETA
    'ι': '\U0001d7b2',  # 𝞲 MATHEMATICAL SANS-SERIF BOLD ITALIC SMALL IOTA
    'κ': '\U0001d7b3',  # 𝞳 MATHEMATICAL SANS-SERIF BOLD ITALIC SMALL KAPPA
    'λ': '\U0001d7b4',  # 𝞴 MATHEMATICAL SANS-SERIF BOLD ITALIC SMALL LAMDA
    'μ': '\U0001d7b5',  # 𝞵 MATHEMATICAL SANS-SERIF BOLD ITALIC SMALL MU
    'ν': '\U0001d7b6',  # 𝞶 MATHEMATICAL SANS-SERIF BOLD ITALIC SMALL NU
    'ξ': '\U0001d7b7',  # 𝞷 MATHEMATICAL SANS-SERIF BOLD ITALIC SMALL XI
    'π': '\U0001d7b9',  # 𝞹 MATHEMATICAL SANS-SERIF BOLD ITALIC SMALL PI
    'ρ': '\U0001d7ba',  # 𝞺 MATHEMATICAL SANS-SERIF BOLD ITALIC SMALL RHO
    'ς': '\U0001d7bb',  # 𝞻 MATHEMATICAL SANS-SERIF BOLD ITALIC SMALL FINAL SIGMA
    'σ': '\U0001d7bc',  # 𝞼 MATHEMATICAL SANS-SERIF BOLD ITALIC SMALL SIGMA
    'τ': '\U0001d7bd',  # 𝞽 MATHEMATICAL SANS-SERIF BOLD ITALIC SMALL TAU
    'υ': '\U0001d7be',  # 𝞾 MATHEMATICAL SANS-SERIF BOLD ITALIC SMALL UPSILON
    'φ': '\U0001d7bf',  # 𝞿 MATHEMATICAL SANS-SERIF BOLD ITALIC SMALL PHI
    'χ': '\U0001d7c0',  # 𝟀 MATHEMATICAL SANS-SERIF BOLD ITALIC SMALL CHI
    'ψ': '\U0001d7c1',  # 𝟁 MATHEMATICAL SANS-SERIF BOLD ITALIC SMALL PSI
    'ω': '\U0001d7c2',  # 𝟂 MATHEMATICAL SANS-SERIF BOLD ITALIC SMALL OMEGA
    'ϑ': '\U0001d7c5',  # 𝟅 MATHEMATICAL SANS-SERIF BOLD ITALIC THETA SYMBOL
    'ϕ': '\U0001d7c7',  # 𝟇 MATHEMATICAL SANS-SERIF BOLD ITALIC PHI SYMBOL
    'ϖ': '\U0001d7c9',  # 𝟉 MATHEMATICAL SANS-SERIF BOLD ITALIC PI SYMBOL
    'ϰ': '\U0001d7c6',  # 𝟆 MATHEMATICAL SANS-SERIF BOLD ITALIC KAPPA SYMBOL
    'ϱ': '\U0001d7c8',  # 𝟈 MATHEMATICAL SANS-SERIF BOLD ITALIC RHO SYMBOL
    'ϵ': '\U0001d7c4',  # 𝟄 MATHEMATICAL SANS-SERIF BOLD ITALIC EPSILON SYMBOL
    '∂': '\U0001d7c3',  # 𝟃 MATHEMATICAL SANS-SERIF BOLD ITALIC PARTIAL DIFFERENTIAL
    '∇': '\U0001d7a9',  # 𝞩 MATHEMATICAL SANS-SERIF BOLD ITALIC NABLA
    }

mathsfit = {
    'A': '\U0001d608',  # 𝘈 MATHEMATICAL SANS-SERIF ITALIC CAPITAL A
    'B': '\U0001d609',  # 𝘉 MATHEMATICAL SANS-SERIF ITALIC CAPITAL B
    'C': '\U0001d60a',  # 𝘊 MATHEMATICAL SANS-SERIF ITALIC CAPITAL C
    'D': '\U0001d60b',  # 𝘋 MATHEMATICAL SANS-SERIF ITALIC CAPITAL D
    'E': '\U0001d60c',  # 𝘌 MATHEMATICAL SANS-SERIF ITALIC CAPITAL E
    'F': '\U0001d60d',  # 𝘍 MATHEMATICAL SANS-SERIF ITALIC CAPITAL F
    'G': '\U0001d60e',  # 𝘎 MATHEMATICAL SANS-SERIF ITALIC CAPITAL G
    'H': '\U0001d60f',  # 𝘏 MATHEMATICAL SANS-SERIF ITALIC CAPITAL H
    'I': '\U0001d610',  # 𝘐 MATHEMATICAL SANS-SERIF ITALIC CAPITAL I
    'J': '\U0001d611',  # 𝘑 MATHEMATICAL SANS-SERIF ITALIC CAPITAL J
    'K': '\U0001d612',  # 𝘒 MATHEMATICAL SANS-SERIF ITALIC CAPITAL K
    'L': '\U0001d613',  # 𝘓 MATHEMATICAL SANS-SERIF ITALIC CAPITAL L
    'M': '\U0001d614',  # 𝘔 MATHEMATICAL SANS-SERIF ITALIC CAPITAL M
    'N': '\U0001d615',  # 𝘕 MATHEMATICAL SANS-SERIF ITALIC CAPITAL N
    'O': '\U0001d616',  # 𝘖 MATHEMATICAL SANS-SERIF ITALIC CAPITAL O
    'P': '\U0001d617',  # 𝘗 MATHEMATICAL SANS-SERIF ITALIC CAPITAL P
    'Q': '\U0001d618',  # 𝘘 MATHEMATICAL SANS-SERIF ITALIC CAPITAL Q
    'R': '\U0001d619',  # 𝘙 MATHEMATICAL SANS-SERIF ITALIC CAPITAL R
    'S': '\U0001d61a',  # 𝘚 MATHEMATICAL SANS-SERIF ITALIC CAPITAL S
    'T': '\U0001d61b',  # 𝘛 MATHEMATICAL SANS-SERIF ITALIC CAPITAL T
    'U': '\U0001d61c',  # 𝘜 MATHEMATICAL SANS-SERIF ITALIC CAPITAL U
    'V': '\U0001d61d',  # 𝘝 MATHEMATICAL SANS-SERIF ITALIC CAPITAL V
    'W': '\U0001d61e',  # 𝘞 MATHEMATICAL SANS-SERIF ITALIC CAPITAL W
    'X': '\U0001d61f',  # 𝘟 MATHEMATICAL SANS-SERIF ITALIC CAPITAL X
    'Y': '\U0001d620',  # 𝘠 MATHEMATICAL SANS-SERIF ITALIC CAPITAL Y
    'Z': '\U0001d621',  # 𝘡 MATHEMATICAL SANS-SERIF ITALIC CAPITAL Z
    'a': '\U0001d622',  # 𝘢 MATHEMATICAL SANS-SERIF ITALIC SMALL A
    'b': '\U0001d623',  # 𝘣 MATHEMATICAL SANS-SERIF ITALIC SMALL B
    'c': '\U0001d624',  # 𝘤 MATHEMATICAL SANS-SERIF ITALIC SMALL C
    'd': '\U0001d625',  # 𝘥 MATHEMATICAL SANS-SERIF ITALIC SMALL D
    'e': '\U0001d626',  # 𝘦 MATHEMATICAL SANS-SERIF ITALIC SMALL E
    'f': '\U0001d627',  # 𝘧 MATHEMATICAL SANS-SERIF ITALIC SMALL F
    'g': '\U0001d628',  # 𝘨 MATHEMATICAL SANS-SERIF ITALIC SMALL G
    'h': '\U0001d629',  # 𝘩 MATHEMATICAL SANS-SERIF ITALIC SMALL H
    'i': '\U0001d62a',  # 𝘪 MATHEMATICAL SANS-SERIF ITALIC SMALL I
    'j': '\U0001d62b',  # 𝘫 MATHEMATICAL SANS-SERIF ITALIC SMALL J
    'k': '\U0001d62c',  # 𝘬 MATHEMATICAL SANS-SERIF ITALIC SMALL K
    'l': '\U0001d62d',  # 𝘭 MATHEMATICAL SANS-SERIF ITALIC SMALL L
    'm': '\U0001d62e',  # 𝘮 MATHEMATICAL SANS-SERIF ITALIC SMALL M
    'n': '\U0001d62f',  # 𝘯 MATHEMATICAL SANS-SERIF ITALIC SMALL N
    'o': '\U0001d630',  # 𝘰 MATHEMATICAL SANS-SERIF ITALIC SMALL O
    'p': '\U0001d631',  # 𝘱 MATHEMATICAL SANS-SERIF ITALIC SMALL P
    'q': '\U0001d632',  # 𝘲 MATHEMATICAL SANS-SERIF ITALIC SMALL Q
    'r': '\U0001d633',  # 𝘳 MATHEMATICAL SANS-SERIF ITALIC SMALL R
    's': '\U0001d634',  # 𝘴 MATHEMATICAL SANS-SERIF ITALIC SMALL S
    't': '\U0001d635',  # 𝘵 MATHEMATICAL SANS-SERIF ITALIC SMALL T
    'u': '\U0001d636',  # 𝘶 MATHEMATICAL SANS-SERIF ITALIC SMALL U
    'v': '\U0001d637',  # 𝘷 MATHEMATICAL SANS-SERIF ITALIC SMALL V
    'w': '\U0001d638',  # 𝘸 MATHEMATICAL SANS-SERIF ITALIC SMALL W
    'x': '\U0001d639',  # 𝘹 MATHEMATICAL SANS-SERIF ITALIC SMALL X
    'y': '\U0001d63a',  # 𝘺 MATHEMATICAL SANS-SERIF ITALIC SMALL Y
    'z': '\U0001d63b',  # 𝘻 MATHEMATICAL SANS-SERIF ITALIC SMALL Z
    }

mathtt = {
    '0': '\U0001d7f6',  # 𝟶 MATHEMATICAL MONOSPACE DIGIT ZERO
    '1': '\U0001d7f7',  # 𝟷 MATHEMATICAL MONOSPACE DIGIT ONE
    '2': '\U0001d7f8',  # 𝟸 MATHEMATICAL MONOSPACE DIGIT TWO
    '3': '\U0001d7f9',  # 𝟹 MATHEMATICAL MONOSPACE DIGIT THREE
    '4': '\U0001d7fa',  # 𝟺 MATHEMATICAL MONOSPACE DIGIT FOUR
    '5': '\U0001d7fb',  # 𝟻 MATHEMATICAL MONOSPACE DIGIT FIVE
    '6': '\U0001d7fc',  # 𝟼 MATHEMATICAL MONOSPACE DIGIT SIX
    '7': '\U0001d7fd',  # 𝟽 MATHEMATICAL MONOSPACE DIGIT SEVEN
    '8': '\U0001d7fe',  # 𝟾 MATHEMATICAL MONOSPACE DIGIT EIGHT
    '9': '\U0001d7ff',  # 𝟿 MATHEMATICAL MONOSPACE DIGIT NINE
    'A': '\U0001d670',  # 𝙰 MATHEMATICAL MONOSPACE CAPITAL A
    'B': '\U0001d671',  # 𝙱 MATHEMATICAL MONOSPACE CAPITAL B
    'C': '\U0001d672',  # 𝙲 MATHEMATICAL MONOSPACE CAPITAL C
    'D': '\U0001d673',  # 𝙳 MATHEMATICAL MONOSPACE CAPITAL D
    'E': '\U0001d674',  # 𝙴 MATHEMATICAL MONOSPACE CAPITAL E
    'F': '\U0001d675',  # 𝙵 MATHEMATICAL MONOSPACE CAPITAL F
    'G': '\U0001d676',  # 𝙶 MATHEMATICAL MONOSPACE CAPITAL G
    'H': '\U0001d677',  # 𝙷 MATHEMATICAL MONOSPACE CAPITAL H
    'I': '\U0001d678',  # 𝙸 MATHEMATICAL MONOSPACE CAPITAL I
    'J': '\U0001d679',  # 𝙹 MATHEMATICAL MONOSPACE CAPITAL J
    'K': '\U0001d67a',  # 𝙺 MATHEMATICAL MONOSPACE CAPITAL K
    'L': '\U0001d67b',  # 𝙻 MATHEMATICAL MONOSPACE CAPITAL L
    'M': '\U0001d67c',  # 𝙼 MATHEMATICAL MONOSPACE CAPITAL M
    'N': '\U0001d67d',  # 𝙽 MATHEMATICAL MONOSPACE CAPITAL N
    'O': '\U0001d67e',  # 𝙾 MATHEMATICAL MONOSPACE CAPITAL O
    'P': '\U0001d67f',  # 𝙿 MATHEMATICAL MONOSPACE CAPITAL P
    'Q': '\U0001d680',  # 𝚀 MATHEMATICAL MONOSPACE CAPITAL Q
    'R': '\U0001d681',  # 𝚁 MATHEMATICAL MONOSPACE CAPITAL R
    'S': '\U0001d682',  # 𝚂 MATHEMATICAL MONOSPACE CAPITAL S
    'T': '\U0001d683',  # 𝚃 MATHEMATICAL MONOSPACE CAPITAL T
    'U': '\U0001d684',  # 𝚄 MATHEMATICAL MONOSPACE CAPITAL U
    'V': '\U0001d685',  # 𝚅 MATHEMATICAL MONOSPACE CAPITAL V
    'W': '\U0001d686',  # 𝚆 MATHEMATICAL MONOSPACE CAPITAL W
    'X': '\U0001d687',  # 𝚇 MATHEMATICAL MONOSPACE CAPITAL X
    'Y': '\U0001d688',  # 𝚈 MATHEMATICAL MONOSPACE CAPITAL Y
    'Z': '\U0001d689',  # 𝚉 MATHEMATICAL MONOSPACE CAPITAL Z
    'a': '\U0001d68a',  # 𝚊 MATHEMATICAL MONOSPACE SMALL A
    'b': '\U0001d68b',  # 𝚋 MATHEMATICAL MONOSPACE SMALL B
    'c': '\U0001d68c',  # 𝚌 MATHEMATICAL MONOSPACE SMALL C
    'd': '\U0001d68d',  # 𝚍 MATHEMATICAL MONOSPACE SMALL D
    'e': '\U0001d68e',  # 𝚎 MATHEMATICAL MONOSPACE SMALL E
    'f': '\U0001d68f',  # 𝚏 MATHEMATICAL MONOSPACE SMALL F
    'g': '\U0001d690',  # 𝚐 MATHEMATICAL MONOSPACE SMALL G
    'h': '\U0001d691',  # 𝚑 MATHEMATICAL MONOSPACE SMALL H
    'i': '\U0001d692',  # 𝚒 MATHEMATICAL MONOSPACE SMALL I
    'j': '\U0001d693',  # 𝚓 MATHEMATICAL MONOSPACE SMALL J
    'k': '\U0001d694',  # 𝚔 MATHEMATICAL MONOSPACE SMALL K
    'l': '\U0001d695',  # 𝚕 MATHEMATICAL MONOSPACE SMALL L
    'm': '\U0001d696',  # 𝚖 MATHEMATICAL MONOSPACE SMALL M
    'n': '\U0001d697',  # 𝚗 MATHEMATICAL MONOSPACE SMALL N
    'o': '\U0001d698',  # 𝚘 MATHEMATICAL MONOSPACE SMALL O
    'p': '\U0001d699',  # 𝚙 MATHEMATICAL MONOSPACE SMALL P
    'q': '\U0001d69a',  # 𝚚 MATHEMATICAL MONOSPACE SMALL Q
    'r': '\U0001d69b',  # 𝚛 MATHEMATICAL MONOSPACE SMALL R
    's': '\U0001d69c',  # 𝚜 MATHEMATICAL MONOSPACE SMALL S
    't': '\U0001d69d',  # 𝚝 MATHEMATICAL MONOSPACE SMALL T
    'u': '\U0001d69e',  # 𝚞 MATHEMATICAL MONOSPACE SMALL U
    'v': '\U0001d69f',  # 𝚟 MATHEMATICAL MONOSPACE SMALL V
    'w': '\U0001d6a0',  # 𝚠 MATHEMATICAL MONOSPACE SMALL W
    'x': '\U0001d6a1',  # 𝚡 MATHEMATICAL MONOSPACE SMALL X
    'y': '\U0001d6a2',  # 𝚢 MATHEMATICAL MONOSPACE SMALL Y
    'z': '\U0001d6a3',  # 𝚣 MATHEMATICAL MONOSPACE SMALL Z
    }
