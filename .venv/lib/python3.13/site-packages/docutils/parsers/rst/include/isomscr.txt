.. This data file has been placed in the public domain.
.. Derived from the Unicode character mappings available from
   <http://www.w3.org/2003/entities/xml/>.
   Processed by unicode2rstsubs.py, part of Docutils:
   <https://docutils.sourceforge.io>.

.. |Bscr| unicode:: U+0212C .. SCRIPT CAPITAL B
.. |Escr| unicode:: U+02130 .. SCRIPT CAPITAL E
.. |escr| unicode:: U+0212F .. SCRIPT SMALL E
.. |Fscr| unicode:: U+02131 .. SCRIPT CAPITAL F
.. |gscr| unicode:: U+0210A .. SCRIPT SMALL G
.. |Hscr| unicode:: U+0210B .. SCRIPT CAPITAL H
.. |Iscr| unicode:: U+02110 .. SCRIPT CAPITAL I
.. |Lscr| unicode:: U+02112 .. SCRIPT CAPITAL L
.. |Mscr| unicode:: U+02133 .. SCRIPT CAPITAL M
.. |oscr| unicode:: U+02134 .. SCRIPT SMALL O
.. |Rscr| unicode:: U+0211B .. SCRIPT CAPITAL R
