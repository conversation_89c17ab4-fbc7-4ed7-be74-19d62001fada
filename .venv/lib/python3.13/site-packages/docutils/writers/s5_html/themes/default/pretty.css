/* This file has been placed in the public domain. */
/* Following are the presentation styles -- edit away! */

html, body {margin: 0; padding: 0;}
body {background: white; color: black;}
/* Replace the background style above with the style below (and again for
   div#header) for a graphic: */
/* background: white url(bodybg.gif) -16px 0 no-repeat; */
:link, :visited {text-decoration: none; color: #00C;}
#controls :active {color: #88A !important;}
#controls :focus {outline: 1px dotted #227;}
h1, h2, h3, h4 {font-size: 100%; margin: 0; padding: 0; font-weight: inherit;}

blockquote {padding: 0 2em 0.5em; margin: 0 1.5em 0.5em;}
blockquote p {margin: 0;}

kbd {font-weight: bold; font-size: 1em;}
sup {font-size: smaller; line-height: 1px;}

.slide pre {padding: 0; margin-left: 0; margin-right: 0; font-size: 90%;}
.slide ul ul li {list-style: square;}
.slide img.leader {display: block; margin: 0 auto;}
.slide tt {font-size: 90%;}

div#header, div#footer {background: #005; color: #AAB; font-family: sans-serif;}
/* background: #005 url(bodybg.gif) -16px 0 no-repeat; */
div#footer {font-size: 0.5em; font-weight: bold; padding: 1em 0;}
#footer h1 {display: block; padding: 0 1em;}
#footer h2 {display: block; padding: 0.8em 1em 0;}

.slide {font-size: 1.2em;}
.slide h1 {position: absolute; top: 0.45em; z-index: 1;
  margin: 0; padding-left: 0.7em; white-space: nowrap;
  font: bold 150% sans-serif; color: #DDE; background: #005;}
.slide h2 {font: bold 120%/1em sans-serif; padding-top: 0.5em;}
.slide h3 {font: bold 100% sans-serif; padding-top: 0.5em;}
h1 abbr {font-variant: small-caps;}

div#controls {position: absolute; left: 50%; bottom: 0;
  width: 50%; text-align: right; font: bold 0.9em sans-serif;}
html>body div#controls {position: fixed; padding: 0 0 1em 0; top: auto;}
div#controls form {position: absolute; bottom: 0; right: 0; width: 100%;
  margin: 0; padding: 0;}
#controls #navLinks a {padding: 0; margin: 0 0.5em;
  background: #005; border: none; color: #779; cursor: pointer;}
#controls #navList {height: 1em;}
#controls #navList #jumplist {position: absolute; bottom: 0; right: 0;
  background: #DDD; color: #227;}

#currentSlide {text-align: center; font-size: 0.5em; color: #449;
  font-family: sans-serif; font-weight: bold;}

#slide0 {padding-top: 1.5em}
#slide0 h1 {position: static; margin: 1em 0 0; padding: 0; color: #000;
  font: bold 2em sans-serif; white-space: normal; background: transparent;}
#slide0 h2 {font: bold italic 1em sans-serif; margin: 0.25em;}
#slide0 h3 {margin-top: 1.5em; font-size: 1.5em;}
#slide0 h4 {margin-top: 0; font-size: 1em;}

ul.urls {list-style: none; display: inline; margin: 0;}
.urls li {display: inline; margin: 0;}
.external {border-bottom: 1px dotted gray;}
html>body .external {border-bottom: none;}
.external:after {content: " \274F"; font-size: smaller; color: #77B;}

.incremental, .incremental *, .incremental *:after {visibility: visible;
  color: white; border: 0;}
img.incremental {visibility: hidden;}
.slide .current {color: green;}

.slide-display {display: inline ! important;}

.huge {font-family: sans-serif; font-weight: bold; font-size: 150%;}
.big {font-family: sans-serif; font-weight: bold; font-size: 120%;}
.small {font-size: 75%;}
.tiny {font-size: 50%;}
.huge tt, .big tt, .small tt, .tiny tt {font-size: 115%;}
.huge pre, .big pre, .small pre, .tiny pre {font-size: 115%;}

.maroon {color: maroon;}
.red {color: red;}
.magenta {color: magenta;}
.fuchsia {color: fuchsia;}
.pink {color: #FAA;}
.orange {color: orange;}
.yellow {color: yellow;}
.lime {color: lime;}
.green {color: green;}
.olive {color: olive;}
.teal {color: teal;}
.cyan {color: cyan;}
.aqua {color: aqua;}
.blue {color: blue;}
.navy {color: navy;}
.purple {color: purple;}
.black {color: black;}
.gray {color: gray;}
.silver {color: silver;}
.white {color: white;}

.left {text-align: left ! important;}
.center {text-align: center ! important;}
.right {text-align: right ! important;}

.animation {position: relative; margin: 1em 0; padding: 0;}
.animation img {position: absolute;}

/* Docutils-specific overrides */

.slide table.docinfo {margin: 1em 0 0.5em 2em;}

pre.literal-block, pre.doctest-block {background-color: white;}

tt.docutils {background-color: white;}

/* diagnostics */
/*
li:after {content: " [" attr(class) "]"; color: #F88;}
div:before {content: "[" attr(class) "]"; color: #F88;}
*/
