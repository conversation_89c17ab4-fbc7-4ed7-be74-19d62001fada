# 网页变动监控服务

这个服务用于监控指定网页的内容变动，特别设计用于监控Android开发者文档页面，但可以用于任何网站。

## 功能特性

- 🕘 **定时监控**: 每天上午9点自动检查页面变动
- 📊 **变动检测**: 使用内容哈希检测页面正文变化
- 📝 **详细差异**: 提供变动内容的详细对比
- 🌐 **HTTP API**: 通过HTTP接口进行控制和查询
- 💾 **持久化存储**: 自动保存页面状态到本地文件
- 🔄 **多重获取方式**: 支持curl和urllib两种网页获取方式

## 监控的页面

### Android开发者文档（目标页面）
1. [Google Play 结算集成](https://developer.android.com/google/play/billing/integrate)
2. [订阅和附加组件](https://developer.android.com/google/play/billing/subscription-with-addons)
3. [订阅生命周期](https://developer.android.com/google/play/billing/lifecycle/subscriptions)
4. [价格变更](https://developer.android.com/google/play/billing/price-changes)
5. [管理购买](https://developer.android.com/google/play/billing/manage-purchases)

### 测试页面
- [HTTPBin HTML](https://httpbin.org/html)
- [Example.com](https://example.com)
- [HTTPBin JSON](https://httpbin.org/json)

## 重要说明

⚠️ **Android开发者网站访问限制**: Android开发者网站有严格的反爬虫机制，可能无法直接通过自动化工具访问。如果需要监控这些页面，建议：

1. 使用代理服务器
2. 配置浏览器自动化工具（如Selenium）
3. 使用专业的网页监控服务
4. 手动定期检查并更新

## 安装依赖

此项目使用Python标准库，无需安装额外依赖。

## 运行服务

```bash
python3 autoWeb.py
```

服务将在端口9001上启动，并自动开始监控网页变动。

## HTTP API接口

服务启动后，可以通过以下HTTP接口进行操作：

### 1. 主页
- **URL**: `GET http://localhost:9001/`
- **描述**: 显示服务主页和可用接口列表

### 2. 获取监控状态
- **URL**: `GET http://localhost:9001/status`
- **描述**: 获取监控服务状态和历史记录

### 3. 立即检查页面变动
- **URL**: `GET http://localhost:9001/check`
- **描述**: 立即检查所有监控页面的变动

### 4. 启动监控服务
- **URL**: `GET http://localhost:9001/start`
- **描述**: 启动网页监控服务

### 5. 获取监控URL列表
- **URL**: `GET http://localhost:9001/urls`
- **描述**: 获取当前监控的所有URL列表

### 6. 添加新的监控URL
- **URL**: `GET http://localhost:9001/add_url?url=URL`
- **描述**: 添加新的URL到监控列表
- **示例**: `http://localhost:9001/add_url?url=https://example.com`

## 文件说明

- `autoWeb.py`: 主服务文件
- `requirements.txt`: Python依赖包
- `webpage_hashes.json`: 存储页面内容哈希值（自动生成）

## 工作原理

1. 服务启动时自动开始监控
2. 每天9:00执行定时检查
3. 获取页面正文内容并计算MD5哈希
4. 与历史哈希值比较检测变动
5. 发现变动时输出详细的差异内容
6. 将新的哈希值保存到本地文件

## 使用示例

```bash
# 启动服务
python3 autoWeb.py

# 查看监控状态
curl http://localhost:9001/status

# 立即检查页面变动
curl http://localhost:9001/check

# 添加新的监控URL
curl "http://localhost:9001/add_url?url=https://example.com"

# 获取所有监控URL
curl http://localhost:9001/urls
```

## 注意事项

- 首次运行会对所有页面进行初始检查
- 页面内容变动检测基于正文内容，忽略样式和脚本
- 服务需要稳定的网络连接
- 建议在服务器环境中运行以确保定时任务的可靠性
- Android开发者网站有反爬虫机制，可能需要特殊配置才能访问
- 可以通过HTTP接口动态添加和管理监控URL
