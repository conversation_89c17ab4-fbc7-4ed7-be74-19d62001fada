# Android开发者文档监控MCP服务

这个MCP服务用于监控Android开发者文档页面的变动，特别是Google Play Billing相关的文档页面。

## 功能特性

- 🕘 **定时监控**: 每天上午9点自动检查页面变动
- 📊 **变动检测**: 使用内容哈希检测页面正文变化
- 📝 **详细差异**: 提供变动内容的详细对比
- 🔧 **MCP接口**: 通过MCP工具函数进行控制和查询
- 💾 **持久化存储**: 自动保存页面状态到本地文件

## 监控的页面

1. [Google Play 结算集成](https://developer.android.com/google/play/billing/integrate?hl=zh-cn)
2. [订阅和附加组件](https://developer.android.com/google/play/billing/subscription-with-addons?hl=zh-cn)
3. [订阅生命周期](https://developer.android.com/google/play/billing/lifecycle/subscriptions?hl=zh-cn)
4. [价格变更](https://developer.android.com/google/play/billing/price-changes?hl=zh-cn)
5. [管理购买](https://developer.android.com/google/play/billing/manage-purchases?hl=zh-cn)

## 安装依赖

此项目使用Python标准库，无需安装额外依赖。

## 运行服务

```bash
python3 autoWeb.py
```

服务将在端口9001上启动，并自动开始监控网页变动。

## HTTP API接口

服务启动后，可以通过以下HTTP接口进行操作：

### 1. 主页
- **URL**: `GET http://localhost:9001/`
- **描述**: 显示服务主页和可用接口列表

### 2. 获取监控状态
- **URL**: `GET http://localhost:9001/status`
- **描述**: 获取监控服务状态和历史记录

### 3. 立即检查页面变动
- **URL**: `GET http://localhost:9001/check`
- **描述**: 立即检查所有监控页面的变动

### 4. 启动监控服务
- **URL**: `GET http://localhost:9001/start`
- **描述**: 启动网页监控服务

### 5. 原有的Android支付代码生成工具
代码中包含以下函数，可以通过编程方式调用：
- `generate_java_init_android_code()`
- `generate_java_product_android_code()`
- `generate_java_pay_android_code()`

## 文件说明

- `autoWeb.py`: 主服务文件
- `requirements.txt`: Python依赖包
- `webpage_hashes.json`: 存储页面内容哈希值（自动生成）

## 工作原理

1. 服务启动时自动开始监控
2. 每天9:00执行定时检查
3. 获取页面正文内容并计算MD5哈希
4. 与历史哈希值比较检测变动
5. 发现变动时输出详细的差异内容
6. 将新的哈希值保存到本地文件

## 注意事项

- 首次运行会对所有页面进行初始检查
- 页面内容变动检测基于正文内容，忽略样式和脚本
- 服务需要稳定的网络连接访问Android开发者文档
- 建议在服务器环境中运行以确保定时任务的可靠性
