#!/bin/bash

# 安装Selenium和WebDriver的脚本

echo "正在安装Selenium和相关依赖..."

# 安装Python包
pip3 install selenium webdriver-manager

# 检查Chrome是否已安装
if command -v google-chrome &> /dev/null || command -v chromium-browser &> /dev/null; then
    echo "✅ Chrome浏览器已安装"
else
    echo "⚠️  未检测到Chrome浏览器"
    echo "请手动安装Chrome浏览器："
    echo "macOS: brew install --cask google-chrome"
    echo "Ubuntu: sudo apt-get install google-chrome-stable"
    echo "或访问: https://www.google.com/chrome/"
fi

echo ""
echo "安装完成！现在可以使用Selenium方法绕过反爬虫机制。"
echo "重新启动服务以使用新功能："
echo "python3 autoWeb.py"
